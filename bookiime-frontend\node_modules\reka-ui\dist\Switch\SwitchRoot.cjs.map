{"version": 3, "file": "SwitchRoot.cjs", "sources": ["../../src/Switch/SwitchRoot.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport type { PrimitiveProps } from '@/Primitive'\nimport type { FormFieldProps } from '@/shared/types'\nimport { createContext, useFormControl, useForwardExpose } from '@/shared'\n\nexport interface SwitchRootProps extends PrimitiveProps, FormFieldProps {\n  /** The state of the switch when it is initially rendered. Use when you do not need to control its state. */\n  defaultValue?: boolean\n  /** The controlled state of the switch. Can be bind as `v-model`. */\n  modelValue?: boolean | null\n  /** When `true`, prevents the user from interacting with the switch. */\n  disabled?: boolean\n  id?: string\n  /** The value given as data when submitted with a `name`. */\n  value?: string\n}\n\nexport type SwitchRootEmits = {\n  /** Event handler called when the value of the switch changes. */\n  'update:modelValue': [payload: boolean]\n}\n\nexport interface SwitchRootContext {\n  modelValue?: Ref<boolean>\n  toggleCheck: () => void\n  disabled: Ref<boolean>\n}\n\nexport const [injectSwitchRootContext, provideSwitchRootContext]\n  = createContext<SwitchRootContext>('SwitchRoot')\n</script>\n\n<script setup lang=\"ts\">\nimport { useVModel } from '@vueuse/core'\nimport { computed, toRefs } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { VisuallyHiddenInput } from '@/VisuallyHidden'\n\nconst props = withDefaults(defineProps<SwitchRootProps>(), {\n  as: 'button',\n  modelValue: undefined,\n  value: 'on',\n})\nconst emit = defineEmits<SwitchRootEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current value */\n    modelValue: typeof modelValue.value\n  }) => any\n}>()\n\nconst { disabled } = toRefs(props)\n\nconst modelValue = useVModel(props, 'modelValue', emit, {\n  defaultValue: props.defaultValue,\n  passive: (props.modelValue === undefined) as false,\n}) as Ref<boolean>\n\nfunction toggleCheck() {\n  if (disabled.value)\n    return\n\n  modelValue.value = !modelValue.value\n}\n\nconst { forwardRef, currentElement } = useForwardExpose()\nconst isFormControl = useFormControl(currentElement)\nconst ariaLabel = computed(() => props.id && currentElement.value ? (document.querySelector(`[for=\"${props.id}\"]`) as HTMLLabelElement)?.innerText : undefined)\n\nprovideSwitchRootContext({\n  modelValue,\n  toggleCheck,\n  disabled,\n})\n</script>\n\n<template>\n  <Primitive\n    v-bind=\"$attrs\"\n    :id=\"id\"\n    :ref=\"forwardRef\"\n    role=\"switch\"\n    :type=\"as === 'button' ? 'button' : undefined\"\n    :value=\"value\"\n    :aria-label=\"$attrs['aria-label'] || ariaLabel\"\n    :aria-checked=\"modelValue\"\n    :aria-required=\"required\"\n    :data-state=\"modelValue ? 'checked' : 'unchecked'\"\n    :data-disabled=\"disabled ? '' : undefined\"\n    :as-child=\"asChild\"\n    :as=\"as\"\n    :disabled=\"disabled\"\n    @click=\"toggleCheck\"\n    @keydown.enter.prevent=\"toggleCheck\"\n  >\n    <slot :model-value=\"modelValue\" />\n\n    <VisuallyHiddenInput\n      v-if=\"isFormControl && name\"\n      type=\"checkbox\"\n      :name=\"name\"\n      :disabled=\"disabled\"\n      :required=\"required\"\n      :value=\"value\"\n      :checked=\"!!modelValue\"\n    />\n  </Primitive>\n</template>\n"], "names": ["createContext", "toRefs", "useVModel", "useForwardExpose", "useFormControl", "computed"], "mappings": ";;;;;;;;;;AA6BO,MAAM,CAAC,uBAAA,EAAyB,wBAAwB,CAAA,GAC3DA,mCAAiC,YAAY;;;;;;;;;;;;;;;;AASjD,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,IAAO,GAAA,MAAA;AASb,IAAA,MAAM,EAAE,QAAA,EAAa,GAAAC,UAAA,CAAO,KAAK,CAAA;AAEjC,IAAA,MAAM,UAAa,GAAAC,cAAA,CAAU,KAAO,EAAA,YAAA,EAAc,IAAM,EAAA;AAAA,MACtD,cAAc,KAAM,CAAA,YAAA;AAAA,MACpB,OAAA,EAAU,MAAM,UAAe,KAAA;AAAA,KAChC,CAAA;AAED,IAAA,SAAS,WAAc,GAAA;AACrB,MAAA,IAAI,QAAS,CAAA,KAAA;AACX,QAAA;AAEF,MAAW,UAAA,CAAA,KAAA,GAAQ,CAAC,UAAW,CAAA,KAAA;AAAA;AAGjC,IAAA,MAAM,EAAE,UAAA,EAAY,cAAe,EAAA,GAAIC,wCAAiB,EAAA;AACxD,IAAM,MAAA,aAAA,GAAgBC,qCAAe,cAAc,CAAA;AACnD,IAAA,MAAM,SAAY,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,MAAM,cAAe,CAAA,KAAA,GAAS,QAAS,CAAA,aAAA,CAAc,SAAS,KAAM,CAAA,EAAE,CAAI,EAAA,CAAA,CAAA,EAAwB,YAAY,MAAS,CAAA;AAE9J,IAAyB,wBAAA,CAAA;AAAA,MACvB,UAAA;AAAA,MACA,WAAA;AAAA,MACA;AAAA,KACD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}