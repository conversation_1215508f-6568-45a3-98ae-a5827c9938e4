{"version": 3, "file": "StepperItem.js", "sources": ["../../src/Stepper/StepperItem.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\n\nimport type { PrimitiveProps } from '@/Primitive'\nimport { computed, toRefs } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { createContext, useForwardExpose, useId } from '@/shared'\nimport { injectStepperRootContext } from './StepperRoot.vue'\n\nexport const [injectStepperItemContext, provideStepperItemContext] = createContext<StepperItemContext>('StepperItem')\n\nexport type StepperState = 'completed' | 'active' | 'inactive'\n\nexport interface StepperItemContext {\n  titleId: string\n  descriptionId: string\n  step: Ref<number>\n  state: Ref<StepperState>\n  disabled: Ref<boolean>\n  isFocusable: Ref<boolean>\n}\n\nexport interface StepperItemProps extends PrimitiveProps {\n  /** A unique value that associates the stepper item with an index */\n  step: number\n  /** When `true`, prevents the user from interacting with the step. */\n  disabled?: boolean\n  /** Shows whether the step is completed. */\n  completed?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nconst props = withDefaults(defineProps<StepperItemProps>(), {\n  completed: false,\n  disabled: false,\n})\n\ndefineSlots<{\n  default?: (props: {\n    /** The current state of the stepper item */\n    state: StepperState\n  }) => any\n}>()\n\nconst { disabled, step, completed } = toRefs(props)\n\nconst { forwardRef } = useForwardExpose()\n\nconst rootContext = injectStepperRootContext()\n\nconst titleId = useId(undefined, 'reka-stepper-item-title')\nconst descriptionId = useId(undefined, 'reka-stepper-item-description')\n\nconst itemState = computed(() => {\n  if (completed.value)\n    return 'completed'\n  if (rootContext.modelValue.value === step.value)\n    return 'active'\n  if (rootContext.modelValue.value! > step.value)\n    return 'completed'\n  return 'inactive'\n})\n\nconst isFocusable = computed(() => {\n  if (disabled.value)\n    return false\n  if (rootContext.linear.value)\n    return step.value <= rootContext.modelValue.value! || step.value === rootContext.modelValue.value! + 1\n\n  return true\n})\n\nprovideStepperItemContext({\n  titleId,\n  descriptionId,\n  state: itemState,\n  disabled,\n  step,\n  isFocusable,\n})\n</script>\n\n<template>\n  <Primitive\n    :ref=\"forwardRef\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    :aria-current=\"itemState === 'active' ? 'true' : undefined\"\n    :data-state=\"itemState\"\n    :disabled=\"disabled || !isFocusable ? '' : undefined\"\n    :data-disabled=\"disabled || !isFocusable ? '' : undefined\"\n    :data-orientation=\"rootContext.orientation.value\"\n  >\n    <slot :state=\"itemState\" />\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;AASO,MAAM,CAAC,wBAAA,EAA0B,yBAAyB,CAAA,GAAI,cAAkC,aAAa;;;;;;;;;;;AAwBpH,IAAA,MAAM,KAAQ,GAAA,OAAA;AAYd,IAAA,MAAM,EAAE,QAAU,EAAA,IAAA,EAAM,SAAU,EAAA,GAAI,OAAO,KAAK,CAAA;AAElD,IAAM,MAAA,EAAE,UAAW,EAAA,GAAI,gBAAiB,EAAA;AAExC,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAE7C,IAAM,MAAA,OAAA,GAAU,KAAM,CAAA,MAAA,EAAW,yBAAyB,CAAA;AAC1D,IAAM,MAAA,aAAA,GAAgB,KAAM,CAAA,MAAA,EAAW,+BAA+B,CAAA;AAEtE,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAC/B,MAAA,IAAI,SAAU,CAAA,KAAA;AACZ,QAAO,OAAA,WAAA;AACT,MAAI,IAAA,WAAA,CAAY,UAAW,CAAA,KAAA,KAAU,IAAK,CAAA,KAAA;AACxC,QAAO,OAAA,QAAA;AACT,MAAI,IAAA,WAAA,CAAY,UAAW,CAAA,KAAA,GAAS,IAAK,CAAA,KAAA;AACvC,QAAO,OAAA,WAAA;AACT,MAAO,OAAA,UAAA;AAAA,KACR,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAA,IAAI,QAAS,CAAA,KAAA;AACX,QAAO,OAAA,KAAA;AACT,MAAA,IAAI,YAAY,MAAO,CAAA,KAAA;AACrB,QAAO,OAAA,IAAA,CAAK,SAAS,WAAY,CAAA,UAAA,CAAW,SAAU,IAAK,CAAA,KAAA,KAAU,WAAY,CAAA,UAAA,CAAW,KAAS,GAAA,CAAA;AAEvG,MAAO,OAAA,IAAA;AAAA,KACR,CAAA;AAED,IAA0B,yBAAA,CAAA;AAAA,MACxB,OAAA;AAAA,MACA,aAAA;AAAA,MACA,KAAO,EAAA,SAAA;AAAA,MACP,QAAA;AAAA,MACA,IAAA;AAAA,MACA;AAAA,KACD,CAAA;;;;;;;;;;;;;;;;;;;;;;;"}