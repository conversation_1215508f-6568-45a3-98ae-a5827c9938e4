{"version": 3, "file": "SplitterResizeHandle.js", "sources": ["../../src/Splitter/SplitterResizeHandle.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { ref, toRefs, watch, watchEffect } from 'vue'\nimport { useWindowSplitterResizeHandlerBehavior } from './utils/composables/useWindowSplitterBehavior'\n\nexport interface SplitterResizeHandleProps extends PrimitiveProps {\n  /** Resize handle id (unique within group); falls back to `useId` when not provided */\n  id?: string\n  /** Allow this much margin when determining resizable handle hit detection */\n  hitAreaMargins?: PointerHitAreaMargins\n  /** Tabindex for the handle */\n  tabindex?: number\n  /** Disable drag handle */\n  disabled?: boolean\n}\n\nexport type PanelResizeHandleOnDragging = (isDragging: boolean) => void\nexport type ResizeHandlerState = 'drag' | 'hover' | 'inactive'\n\nexport type SplitterResizeHandleEmits = {\n  /** Event handler called when dragging the handler. */\n  dragging: [isDragging: boolean]\n}\n</script>\n\n<script setup lang=\"ts\">\nimport type { PointerHitAreaMargins, ResizeHandlerAction } from './utils/registry'\nimport type { ResizeEvent, ResizeHandler } from './utils/types'\nimport { Primitive } from '@/Primitive'\nimport { isBrowser, useForwardExpose, useId } from '@/shared'\nimport { injectPanelGroupContext } from './SplitterGroup.vue'\nimport { assert } from './utils/assert'\nimport { registerResizeHandle } from './utils/registry'\n\nconst props = withDefaults(defineProps<SplitterResizeHandleProps>(), {\n  tabindex: 0,\n})\nconst emits = defineEmits<SplitterResizeHandleEmits>()\n\nconst { forwardRef, currentElement } = useForwardExpose()\nconst { disabled } = toRefs(props)\n\nconst panelGroupContext = injectPanelGroupContext()\nif (panelGroupContext === null) {\n  throw new Error(\n    'PanelResizeHandle components must be rendered within a PanelGroup container',\n  )\n}\n\nconst {\n  direction,\n  groupId,\n  registerResizeHandle: registerResizeHandleWithParentGroup,\n  startDragging,\n  stopDragging,\n  panelGroupElement,\n} = panelGroupContext\n\nconst resizeHandleId = useId(props.id, 'reka-splitter-resize-handle')\nconst state = ref<ResizeHandlerState>('inactive')\nconst isFocused = ref(false)\nconst resizeHandler = ref<ResizeHandler | null>(null)\n\nwatch(disabled, () => {\n  if (!isBrowser)\n    return\n  if (disabled.value)\n    resizeHandler.value = null\n  else\n    resizeHandler.value = registerResizeHandleWithParentGroup(resizeHandleId)\n}, { immediate: true })\n\nwatchEffect((onCleanup) => {\n  if (disabled.value || resizeHandler.value === null)\n    return\n\n  const element = currentElement.value\n  if (!element)\n    return\n\n  assert(element)\n\n  const setResizeHandlerState = (\n    action: ResizeHandlerAction,\n    isActive: boolean,\n    event: ResizeEvent,\n  ) => {\n    if (isActive) {\n      switch (action) {\n        case 'down': {\n          state.value = 'drag'\n\n          startDragging(resizeHandleId, event)\n          emits('dragging', true)\n          break\n        }\n        case 'move': {\n          if (state.value !== 'drag')\n            state.value = 'hover'\n\n          resizeHandler.value?.(event)\n          break\n        }\n        case 'up': {\n          state.value = 'hover'\n\n          stopDragging()\n          emits('dragging', false)\n          break\n        }\n      }\n    }\n    else {\n      state.value = 'inactive'\n    }\n  }\n\n  onCleanup(registerResizeHandle(\n    resizeHandleId,\n    element,\n    direction,\n    {\n      // Coarse inputs (e.g. finger/touch)\n      coarse: props.hitAreaMargins?.coarse ?? 15,\n      // Fine inputs (e.g. mouse)\n      fine: props.hitAreaMargins?.fine ?? 5,\n    },\n    setResizeHandlerState,\n  ))\n})\n\nuseWindowSplitterResizeHandlerBehavior({\n  disabled,\n  resizeHandler,\n  handleId: resizeHandleId,\n  panelGroupElement,\n})\n</script>\n\n<template>\n  <Primitive\n    :id=\"resizeHandleId\"\n    :ref=\"forwardRef\"\n    :style=\"{\n      touchAction: 'none',\n      userSelect: 'none',\n    }\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    role=\"separator\"\n    data-resize-handle=\"\"\n    :tabindex=\"tabindex\"\n    :data-state=\"state\"\n    :data-disabled=\"disabled ? '' : undefined\"\n    :data-orientation=\"direction\"\n    :data-panel-group-id=\"groupId\"\n    :data-resize-handle-active=\"state === 'drag' ? 'pointer' : isFocused ? 'keyboard' : undefined\"\n    :data-resize-handle-state=\"state\"\n    :data-panel-resize-handle-enabled=\"!disabled\"\n    :data-panel-resize-handle-id=\" resizeHandleId\"\n    @blur=\"isFocused = false\"\n    @focus=\"isFocused = false\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAkCA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAGd,IAAA,MAAM,KAAQ,GAAA,MAAA;AAEd,IAAA,MAAM,EAAE,UAAA,EAAY,cAAe,EAAA,GAAI,gBAAiB,EAAA;AACxD,IAAA,MAAM,EAAE,QAAA,EAAa,GAAA,MAAA,CAAO,KAAK,CAAA;AAEjC,IAAA,MAAM,oBAAoB,uBAAwB,EAAA;AAClD,IAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,MAAA,MAAM,IAAI,KAAA;AAAA,QACR;AAAA,OACF;AAAA;AAGF,IAAM,MAAA;AAAA,MACJ,SAAA;AAAA,MACA,OAAA;AAAA,MACA,oBAAsB,EAAA,mCAAA;AAAA,MACtB,aAAA;AAAA,MACA,YAAA;AAAA,MACA;AAAA,KACE,GAAA,iBAAA;AAEJ,IAAA,MAAM,cAAiB,GAAA,KAAA,CAAM,KAAM,CAAA,EAAA,EAAI,6BAA6B,CAAA;AACpE,IAAM,MAAA,KAAA,GAAQ,IAAwB,UAAU,CAAA;AAChD,IAAM,MAAA,SAAA,GAAY,IAAI,KAAK,CAAA;AAC3B,IAAM,MAAA,aAAA,GAAgB,IAA0B,IAAI,CAAA;AAEpD,IAAA,KAAA,CAAM,UAAU,MAAM;AACpB,MAAA,IAAI,CAAC,SAAA;AACH,QAAA;AACF,MAAA,IAAI,QAAS,CAAA,KAAA;AACX,QAAA,aAAA,CAAc,KAAQ,GAAA,IAAA;AAAA;AAEtB,QAAc,aAAA,CAAA,KAAA,GAAQ,oCAAoC,cAAc,CAAA;AAAA,KACzE,EAAA,EAAE,SAAW,EAAA,IAAA,EAAM,CAAA;AAEtB,IAAA,WAAA,CAAY,CAAC,SAAc,KAAA;AACzB,MAAI,IAAA,QAAA,CAAS,KAAS,IAAA,aAAA,CAAc,KAAU,KAAA,IAAA;AAC5C,QAAA;AAEF,MAAA,MAAM,UAAU,cAAe,CAAA,KAAA;AAC/B,MAAA,IAAI,CAAC,OAAA;AACH,QAAA;AAEF,MAAA,MAAA,CAAO,OAAO,CAAA;AAEd,MAAA,MAAM,qBAAwB,GAAA,CAC5B,MACA,EAAA,QAAA,EACA,KACG,KAAA;AACH,QAAA,IAAI,QAAU,EAAA;AACZ,UAAA,QAAQ,MAAQ;AAAA,YACd,KAAK,MAAQ,EAAA;AACX,cAAA,KAAA,CAAM,KAAQ,GAAA,MAAA;AAEd,cAAA,aAAA,CAAc,gBAAgB,KAAK,CAAA;AACnC,cAAA,KAAA,CAAM,YAAY,IAAI,CAAA;AACtB,cAAA;AAAA;AACF,YACA,KAAK,MAAQ,EAAA;AACX,cAAA,IAAI,MAAM,KAAU,KAAA,MAAA;AAClB,gBAAA,KAAA,CAAM,KAAQ,GAAA,OAAA;AAEhB,cAAA,aAAA,CAAc,QAAQ,KAAK,CAAA;AAC3B,cAAA;AAAA;AACF,YACA,KAAK,IAAM,EAAA;AACT,cAAA,KAAA,CAAM,KAAQ,GAAA,OAAA;AAEd,cAAa,YAAA,EAAA;AACb,cAAA,KAAA,CAAM,YAAY,KAAK,CAAA;AACvB,cAAA;AAAA;AACF;AACF,SAEG,MAAA;AACH,UAAA,KAAA,CAAM,KAAQ,GAAA,UAAA;AAAA;AAChB,OACF;AAEA,MAAU,SAAA,CAAA,oBAAA;AAAA,QACR,cAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA;AAAA;AAAA,UAEE,MAAA,EAAQ,KAAM,CAAA,cAAA,EAAgB,MAAU,IAAA,EAAA;AAAA;AAAA,UAExC,IAAA,EAAM,KAAM,CAAA,cAAA,EAAgB,IAAQ,IAAA;AAAA,SACtC;AAAA,QACA;AAAA,OACD,CAAA;AAAA,KACF,CAAA;AAED,IAAuC,sCAAA,CAAA;AAAA,MACrC,QAAA;AAAA,MACA,aAAA;AAAA,MACA,QAAU,EAAA,cAAA;AAAA,MACV;AAAA,KACD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}