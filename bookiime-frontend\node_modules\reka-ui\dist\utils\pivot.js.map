{"version": 3, "file": "pivot.js", "sources": ["../../src/Splitter/utils/pivot.ts"], "sourcesContent": ["import { getResizeHandleElementIndex } from './dom'\n\nexport function determinePivotIndices(\n  groupId: string,\n  dragHandleId: string,\n  panelGroupElement: ParentNode,\n): [indexBefore: number, indexAfter: number] {\n  const index = getResizeHandleElementIndex(\n    groupId,\n    dragHandleId,\n    panelGroupElement,\n  )\n\n  return index != null ? [index, index + 1] : [-1, -1]\n}\n"], "names": [], "mappings": ";;AAEgB,SAAA,qBAAA,CACd,OACA,EAAA,YAAA,EACA,iBAC2C,EAAA;AAC3C,EAAA,MAAM,KAAQ,GAAA,2BAAA;AAAA,IACZ,OAAA;AAAA,IACA,YAAA;AAAA,IACA;AAAA,GACF;AAEA,EAAO,OAAA,KAAA,IAAS,OAAO,CAAC,KAAA,EAAO,QAAQ,CAAC,CAAA,GAAI,CAAC,EAAA,EAAI,EAAE,CAAA;AACrD;;;;"}