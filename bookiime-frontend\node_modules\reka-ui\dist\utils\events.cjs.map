{"version": 3, "file": "events.cjs", "sources": ["../../src/Splitter/utils/events.ts"], "sourcesContent": ["import type { Direction, ResizeEvent } from './types'\n\nexport function isKeyDown(event: ResizeEvent): event is KeyboardEvent {\n  return event.type === 'keydown'\n}\n\nexport function isMouseEvent(event: ResizeEvent): event is MouseEvent {\n  return event.type.startsWith('mouse')\n}\n\nexport function isTouchEvent(event: ResizeEvent): event is TouchEvent {\n  return event.type.startsWith('touch')\n}\n\nexport function getResizeEventCoordinates(event: ResizeEvent) {\n  if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    }\n  }\n  else if (isTouchEvent(event)) {\n    const touch = event.touches[0]\n    if (touch && touch.clientX && touch.clientY) {\n      return {\n        x: touch.clientX,\n        y: touch.clientY,\n      }\n    }\n  }\n\n  return {\n    x: Number.POSITIVE_INFINITY,\n    y: Number.POSITIVE_INFINITY,\n  }\n}\n\nexport function getResizeEventCursorPosition(\n  direction: Direction,\n  event: ResizeEvent,\n): number {\n  const isHorizontal = direction === 'horizontal'\n\n  const { x, y } = getResizeEventCoordinates(event)\n\n  return isHorizontal ? x : y\n}\n"], "names": [], "mappings": ";;AAEO,SAAS,UAAU,KAA4C,EAAA;AACpE,EAAA,OAAO,MAAM,IAAS,KAAA,SAAA;AACxB;AAEO,SAAS,aAAa,KAAyC,EAAA;AACpE,EAAO,OAAA,KAAA,CAAM,IAAK,CAAA,UAAA,CAAW,OAAO,CAAA;AACtC;AAEO,SAAS,aAAa,KAAyC,EAAA;AACpE,EAAO,OAAA,KAAA,CAAM,IAAK,CAAA,UAAA,CAAW,OAAO,CAAA;AACtC;AAEO,SAAS,0BAA0B,KAAoB,EAAA;AAC5D,EAAI,IAAA,YAAA,CAAa,KAAK,CAAG,EAAA;AACvB,IAAO,OAAA;AAAA,MACL,GAAG,KAAM,CAAA,OAAA;AAAA,MACT,GAAG,KAAM,CAAA;AAAA,KACX;AAAA,GACF,MAAA,IACS,YAAa,CAAA,KAAK,CAAG,EAAA;AAC5B,IAAM,MAAA,KAAA,GAAQ,KAAM,CAAA,OAAA,CAAQ,CAAC,CAAA;AAC7B,IAAA,IAAI,KAAS,IAAA,KAAA,CAAM,OAAW,IAAA,KAAA,CAAM,OAAS,EAAA;AAC3C,MAAO,OAAA;AAAA,QACL,GAAG,KAAM,CAAA,OAAA;AAAA,QACT,GAAG,KAAM,CAAA;AAAA,OACX;AAAA;AACF;AAGF,EAAO,OAAA;AAAA,IACL,GAAG,MAAO,CAAA,iBAAA;AAAA,IACV,GAAG,MAAO,CAAA;AAAA,GACZ;AACF;AAEgB,SAAA,4BAAA,CACd,WACA,KACQ,EAAA;AACR,EAAA,MAAM,eAAe,SAAc,KAAA,YAAA;AAEnC,EAAA,MAAM,EAAE,CAAA,EAAG,CAAE,EAAA,GAAI,0BAA0B,KAAK,CAAA;AAEhD,EAAA,OAAO,eAAe,CAAI,GAAA,CAAA;AAC5B;;;;;;;;"}