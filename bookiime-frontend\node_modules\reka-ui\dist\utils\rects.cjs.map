{"version": 3, "file": "rects.cjs", "sources": ["../../src/Splitter/utils/rects.ts"], "sourcesContent": ["export interface Rectangle {\n  x: number\n  y: number\n  width: number\n  height: number\n}\n\nexport function intersects(\n  rectOne: Rectangle,\n  rectTwo: Rectangle,\n  strict: boolean,\n): boolean {\n  if (strict) {\n    return (\n      rectOne.x < rectTwo.x + rectTwo.width\n      && rectOne.x + rectOne.width > rectTwo.x\n      && rectOne.y < rectTwo.y + rectTwo.height\n      && rectOne.y + rectOne.height > rectTwo.y\n    )\n  }\n  else {\n    return (\n      rectOne.x <= rectTwo.x + rectTwo.width\n      && rectOne.x + rectOne.width >= rectTwo.x\n      && rectOne.y <= rectTwo.y + rectTwo.height\n      && rectOne.y + rectOne.height >= rectTwo.y\n    )\n  }\n}\n\nexport function getIntersectingRectangle(\n  rectOne: Rectangle,\n  rectTwo: Rectangle,\n  strict: boolean,\n): Rectangle {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0,\n    }\n  }\n\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width:\n      Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width)\n      - Math.max(rectOne.x, rectTwo.x),\n    height:\n      Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height)\n      - Math.max(rectOne.y, rectTwo.y),\n  }\n}\n"], "names": [], "mappings": ";;AAOgB,SAAA,UAAA,CACd,OACA,EAAA,OAAA,EACA,MACS,EAAA;AACT,EAAY;AACV,IACE,OAAA,OAAA,CAAQ,IAAI,OAAQ,CAAA,CAAA,GAAI,QAAQ,KAC7B,IAAA,OAAA,CAAQ,CAAI,GAAA,OAAA,CAAQ,KAAQ,GAAA,OAAA,CAAQ,KACpC,OAAQ,CAAA,CAAA,GAAI,QAAQ,CAAI,GAAA,OAAA,CAAQ,UAChC,OAAQ,CAAA,CAAA,GAAI,OAAQ,CAAA,MAAA,GAAS,OAAQ,CAAA,CAAA;AAAA;AAW9C;;;;"}