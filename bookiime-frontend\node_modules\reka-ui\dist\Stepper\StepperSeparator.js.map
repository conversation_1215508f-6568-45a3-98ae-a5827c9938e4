{"version": 3, "file": "StepperSeparator.js", "sources": ["../../src/Stepper/StepperSeparator.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { SeparatorProps } from '@/Separator'\nimport { Separator } from '@/Separator'\nimport { useForwardExpose } from '@/shared'\nimport { injectStepperItemContext } from './StepperItem.vue'\nimport { injectStepperRootContext } from './StepperRoot.vue'\n</script>\n\n<script setup lang=\"ts\">\nexport interface StepperSeparatorProps extends SeparatorProps { }\n\nconst props = withDefaults(defineProps<StepperSeparatorProps>(), {})\n\nconst rootContext = injectStepperRootContext()\nconst itemContext = injectStepperItemContext()\n\nuseForwardExpose()\n</script>\n\n<template>\n  <Separator\n    v-bind=\"props\"\n    decorative\n    :orientation=\"rootContext.orientation.value\"\n    :data-state=\"itemContext.state.value\"\n  >\n    <slot />\n  </Separator>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAC7C,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAE7C,IAAiB,gBAAA,EAAA;;;;;;;;;;;;;;;;;;"}