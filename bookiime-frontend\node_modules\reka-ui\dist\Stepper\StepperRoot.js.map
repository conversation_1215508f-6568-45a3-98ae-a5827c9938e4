{"version": 3, "file": "StepperRoot.js", "sources": ["../../src/Stepper/StepperRoot.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { Ref } from 'vue'\nimport type { DataOrientation, Direction } from '../shared/types'\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useVModel } from '@vueuse/core'\nimport { computed, nextTick, ref, toRefs, watch } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { createContext, useDirection, useForwardExpose } from '@/shared'\n\nexport interface StepperRootContext {\n  modelValue: Ref<number | undefined>\n  changeModelValue: (value: number) => void\n  orientation: Ref<DataOrientation>\n  dir: Ref<Direction>\n  linear: Ref<boolean>\n  totalStepperItems: Ref<Set<HTMLElement>>\n}\n\nexport interface StepperRootProps extends PrimitiveProps {\n  /**\n   * The value of the step that should be active when initially rendered. Use when you do not need to control the state of the steps.\n   */\n  defaultValue?: number\n  /**\n   * The orientation the steps are laid out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down).\n   * @defaultValue horizontal\n   */\n  orientation?: DataOrientation\n  /**\n   * The reading direction of the combobox when applicable. <br> If omitted, inherits globally from `ConfigProvider` or assumes LTR (left-to-right) reading mode.\n   */\n  dir?: Direction\n  /** The controlled value of the step to activate. Can be bound as `v-model`. */\n  modelValue?: number\n  /** Whether or not the steps must be completed in order. */\n  linear?: boolean\n}\nexport type StepperRootEmits = {\n  /** Event handler called when the value changes */\n  'update:modelValue': [payload: number | undefined]\n}\n\nexport const [injectStepperRootContext, provideStepperRootContext]\n  = createContext<StepperRootContext>('StepperRoot')\n</script>\n\n<script setup lang=\"ts\">\nconst props = withDefaults(defineProps<StepperRootProps>(), {\n  orientation: 'horizontal',\n  linear: true,\n  defaultValue: 1,\n})\nconst emits = defineEmits<StepperRootEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current step */\n    modelValue: number | undefined\n    /** Total number of steps */\n    totalSteps: number\n    /** Whether or not the next step is disabled */\n    isNextDisabled: boolean\n    /** Whether or not the previous step is disabled */\n    isPrevDisabled: boolean\n    /** Whether or not the first step is active */\n    isFirstStep: boolean\n    /** Whether or not the last step is active */\n    isLastStep: boolean\n    /** Go to a specific step */\n    goToStep: (step: number) => void\n    /** Go to the next step */\n    nextStep: () => void\n    /** Go to the previous step */\n    prevStep: () => void\n    /** Whether or not there is a next step */\n    hasNext: () => boolean\n    /** Whether or not there is a previous step */\n    hasPrev: () => boolean\n  }) => any\n}>()\n\nconst { dir: propDir, orientation: propOrientation, linear } = toRefs(props)\nconst dir = useDirection(propDir)\n\nconst totalStepperItems = ref<Set<HTMLElement>>(new Set())\n\nconst modelValue = useVModel(props, 'modelValue', emits, {\n  defaultValue: props.defaultValue,\n  passive: (props.modelValue === undefined) as false,\n})\n\nconst totalStepperItemsArray = computed(() => Array.from(totalStepperItems.value))\n\nconst isFirstStep = computed(() => modelValue.value === 1)\nconst isLastStep = computed(() => modelValue.value === totalStepperItemsArray.value.length)\n\nconst totalSteps = computed(() => totalStepperItems.value.size)\n\nfunction goToStep(step: number) {\n  if (step > totalSteps.value)\n    return\n\n  if (step < 1)\n    return\n\n  if (totalStepperItems.value.size && !!totalStepperItemsArray.value[step] && !!totalStepperItemsArray.value[step].getAttribute('disabled'))\n    return\n\n  if (linear.value) {\n    if (step > (modelValue.value ?? 1) + 1)\n      return\n  }\n\n  modelValue.value = step\n}\n\nfunction nextStep() {\n  goToStep((modelValue.value ?? 1) + 1)\n}\n\nfunction prevStep() {\n  goToStep((modelValue.value ?? 1) - 1)\n}\n\nfunction hasNext() {\n  return (modelValue.value ?? 1) < totalSteps.value\n}\n\nfunction hasPrev() {\n  return (modelValue.value ?? 1) > 1\n}\n\nconst nextStepperItem = ref<HTMLElement | null>(null)\nconst prevStepperItem = ref<HTMLElement | null>(null)\nconst isNextDisabled = computed(() => nextStepperItem.value ? nextStepperItem.value.getAttribute('disabled') === '' : true)\nconst isPrevDisabled = computed(() => prevStepperItem.value ? prevStepperItem.value.getAttribute('disabled') === '' : true)\n\nwatch(modelValue, async () => {\n  await nextTick(() => {\n    nextStepperItem.value = totalStepperItemsArray.value.length && modelValue.value! < totalStepperItemsArray.value.length ? totalStepperItemsArray.value[modelValue.value!] : null\n    prevStepperItem.value = totalStepperItemsArray.value.length && modelValue.value! > 1 ? totalStepperItemsArray.value[modelValue.value! - 2] : null\n  })\n})\nwatch(totalStepperItemsArray, async () => {\n  await nextTick(() => {\n    nextStepperItem.value = totalStepperItemsArray.value.length && modelValue.value! < totalStepperItemsArray.value.length ? totalStepperItemsArray.value[modelValue.value!] : null\n    prevStepperItem.value = totalStepperItemsArray.value.length && modelValue.value! > 1 ? totalStepperItemsArray.value[modelValue.value! - 2] : null\n  })\n})\n\nprovideStepperRootContext({\n  modelValue,\n  changeModelValue: (value: number) => {\n    modelValue.value = value\n  },\n  orientation: propOrientation,\n  dir,\n  linear,\n  totalStepperItems,\n})\n\ndefineExpose({\n  goToStep,\n  nextStep,\n  prevStep,\n  modelValue,\n  totalSteps,\n  isNextDisabled,\n  isPrevDisabled,\n  isFirstStep,\n  isLastStep,\n  hasNext,\n  hasPrev,\n})\n\nuseForwardExpose()\n</script>\n\n<template>\n  <Primitive\n    role=\"group\"\n    aria-label=\"progress\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    :data-linear=\"linear ? '' : undefined\"\n    :data-orientation=\"orientation\"\n  >\n    <slot\n      :model-value=\"modelValue\"\n      :total-steps=\"totalStepperItems.size\"\n      :is-next-disabled=\"isNextDisabled\"\n      :is-prev-disabled=\"isPrevDisabled\"\n      :is-first-step=\"isFirstStep\"\n      :is-last-step=\"isLastStep\"\n      :go-to-step=\"goToStep\"\n      :next-step=\"nextStep\"\n      :prev-step=\"prevStep\"\n      :has-next=\"hasNext\"\n      :has-prev=\"hasPrev\"\n    />\n\n    <div\n      aria-live=\"polite\"\n      aria-atomic=\"true\"\n      role=\"status\"\n      :style=\"{\n        transform: 'translateX(-100%)',\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }\"\n    >\n      Step {{ modelValue }} of {{ totalStepperItems.size }}\n    </div>\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA2CO,MAAM,CAAC,wBAAA,EAA0B,yBAAyB,CAAA,GAC7D,cAAkC,aAAa;;;;;;;;;;;;;;AAInD,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,KAAQ,GAAA,MAAA;AA6Bd,IAAM,MAAA,EAAE,KAAK,OAAS,EAAA,WAAA,EAAa,iBAAiB,MAAO,EAAA,GAAI,OAAO,KAAK,CAAA;AAC3E,IAAM,MAAA,GAAA,GAAM,aAAa,OAAO,CAAA;AAEhC,IAAA,MAAM,iBAAoB,GAAA,GAAA,iBAA0B,IAAA,GAAA,EAAK,CAAA;AAEzD,IAAA,MAAM,UAAa,GAAA,SAAA,CAAU,KAAO,EAAA,YAAA,EAAc,KAAO,EAAA;AAAA,MACvD,cAAc,KAAM,CAAA,YAAA;AAAA,MACpB,OAAA,EAAU,MAAM,UAAe,KAAA;AAAA,KAChC,CAAA;AAED,IAAA,MAAM,yBAAyB,QAAS,CAAA,MAAM,MAAM,IAAK,CAAA,iBAAA,CAAkB,KAAK,CAAC,CAAA;AAEjF,IAAA,MAAM,WAAc,GAAA,QAAA,CAAS,MAAM,UAAA,CAAW,UAAU,CAAC,CAAA;AACzD,IAAA,MAAM,aAAa,QAAS,CAAA,MAAM,WAAW,KAAU,KAAA,sBAAA,CAAuB,MAAM,MAAM,CAAA;AAE1F,IAAA,MAAM,UAAa,GAAA,QAAA,CAAS,MAAM,iBAAA,CAAkB,MAAM,IAAI,CAAA;AAE9D,IAAA,SAAS,SAAS,IAAc,EAAA;AAC9B,MAAA,IAAI,OAAO,UAAW,CAAA,KAAA;AACpB,QAAA;AAEF,MAAA,IAAI,IAAO,GAAA,CAAA;AACT,QAAA;AAEF,MAAA,IAAI,kBAAkB,KAAM,CAAA,IAAA,IAAQ,CAAC,CAAC,uBAAuB,KAAM,CAAA,IAAI,CAAK,IAAA,CAAC,CAAC,sBAAuB,CAAA,KAAA,CAAM,IAAI,CAAA,CAAE,aAAa,UAAU,CAAA;AACtI,QAAA;AAEF,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAI,IAAA,IAAA,GAAA,CAAQ,UAAW,CAAA,KAAA,IAAS,CAAK,IAAA,CAAA;AACnC,UAAA;AAAA;AAGJ,MAAA,UAAA,CAAW,KAAQ,GAAA,IAAA;AAAA;AAGrB,IAAA,SAAS,QAAW,GAAA;AAClB,MAAU,QAAA,CAAA,CAAA,UAAA,CAAW,KAAS,IAAA,CAAA,IAAK,CAAC,CAAA;AAAA;AAGtC,IAAA,SAAS,QAAW,GAAA;AAClB,MAAU,QAAA,CAAA,CAAA,UAAA,CAAW,KAAS,IAAA,CAAA,IAAK,CAAC,CAAA;AAAA;AAGtC,IAAA,SAAS,OAAU,GAAA;AACjB,MAAQ,OAAA,CAAA,UAAA,CAAW,KAAS,IAAA,CAAA,IAAK,UAAW,CAAA,KAAA;AAAA;AAG9C,IAAA,SAAS,OAAU,GAAA;AACjB,MAAQ,OAAA,CAAA,UAAA,CAAW,SAAS,CAAK,IAAA,CAAA;AAAA;AAGnC,IAAM,MAAA,eAAA,GAAkB,IAAwB,IAAI,CAAA;AACpD,IAAM,MAAA,eAAA,GAAkB,IAAwB,IAAI,CAAA;AACpD,IAAM,MAAA,cAAA,GAAiB,QAAS,CAAA,MAAM,eAAgB,CAAA,KAAA,GAAQ,eAAgB,CAAA,KAAA,CAAM,YAAa,CAAA,UAAU,CAAM,KAAA,EAAA,GAAK,IAAI,CAAA;AAC1H,IAAM,MAAA,cAAA,GAAiB,QAAS,CAAA,MAAM,eAAgB,CAAA,KAAA,GAAQ,eAAgB,CAAA,KAAA,CAAM,YAAa,CAAA,UAAU,CAAM,KAAA,EAAA,GAAK,IAAI,CAAA;AAE1H,IAAA,KAAA,CAAM,YAAY,YAAY;AAC5B,MAAA,MAAM,SAAS,MAAM;AACnB,QAAA,eAAA,CAAgB,KAAQ,GAAA,sBAAA,CAAuB,KAAM,CAAA,MAAA,IAAU,UAAW,CAAA,KAAA,GAAS,sBAAuB,CAAA,KAAA,CAAM,MAAS,GAAA,sBAAA,CAAuB,KAAM,CAAA,UAAA,CAAW,KAAM,CAAI,GAAA,IAAA;AAC3K,QAAA,eAAA,CAAgB,KAAQ,GAAA,sBAAA,CAAuB,KAAM,CAAA,MAAA,IAAU,UAAW,CAAA,KAAA,GAAS,CAAI,GAAA,sBAAA,CAAuB,KAAM,CAAA,UAAA,CAAW,KAAS,GAAA,CAAC,CAAI,GAAA,IAAA;AAAA,OAC9I,CAAA;AAAA,KACF,CAAA;AACD,IAAA,KAAA,CAAM,wBAAwB,YAAY;AACxC,MAAA,MAAM,SAAS,MAAM;AACnB,QAAA,eAAA,CAAgB,KAAQ,GAAA,sBAAA,CAAuB,KAAM,CAAA,MAAA,IAAU,UAAW,CAAA,KAAA,GAAS,sBAAuB,CAAA,KAAA,CAAM,MAAS,GAAA,sBAAA,CAAuB,KAAM,CAAA,UAAA,CAAW,KAAM,CAAI,GAAA,IAAA;AAC3K,QAAA,eAAA,CAAgB,KAAQ,GAAA,sBAAA,CAAuB,KAAM,CAAA,MAAA,IAAU,UAAW,CAAA,KAAA,GAAS,CAAI,GAAA,sBAAA,CAAuB,KAAM,CAAA,UAAA,CAAW,KAAS,GAAA,CAAC,CAAI,GAAA,IAAA;AAAA,OAC9I,CAAA;AAAA,KACF,CAAA;AAED,IAA0B,yBAAA,CAAA;AAAA,MACxB,UAAA;AAAA,MACA,gBAAA,EAAkB,CAAC,KAAkB,KAAA;AACnC,QAAA,UAAA,CAAW,KAAQ,GAAA,KAAA;AAAA,OACrB;AAAA,MACA,WAAa,EAAA,eAAA;AAAA,MACb,GAAA;AAAA,MACA,MAAA;AAAA,MACA;AAAA,KACD,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,QAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,cAAA;AAAA,MACA,WAAA;AAAA,MACA,UAAA;AAAA,MACA,OAAA;AAAA,MACA;AAAA,KACD,CAAA;AAED,IAAiB,gBAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}