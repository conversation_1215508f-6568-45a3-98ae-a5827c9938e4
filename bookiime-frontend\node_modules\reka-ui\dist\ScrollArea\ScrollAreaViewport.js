import { defineComponent, toRefs, ref, onMounted, createElementBlock, openBlock, Fragment, createElementVNode, createVNode, mergeProps, unref, normalizeStyle, withCtx, renderSlot, createTextVNode } from 'vue';
import { u as useNonce } from '../shared/useNonce.js';
import { u as useForwardExpose } from '../shared/useForwardExpose.js';
import { P as Primitive } from '../Primitive/Primitive.js';
import { i as injectScrollAreaRootContext } from './ScrollAreaRoot.js';

const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    inheritAttrs: false
  },
  __name: "ScrollAreaViewport",
  props: {
    nonce: {},
    asChild: { type: Boolean },
    as: {}
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const { nonce: propNonce } = toRefs(props);
    const nonce = useNonce(propNonce);
    const rootContext = injectScrollAreaRootContext();
    const viewportElement = ref();
    onMounted(() => {
      rootContext.onViewportChange(viewportElement.value);
      rootContext.onContentChange(contentElement.value);
    });
    __expose({
      viewportElement
    });
    const { forwardRef, currentElement: contentElement } = useForwardExpose();
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(Fragment, null, [
        createElementVNode("div", mergeProps({
          ref_key: "viewportElement",
          ref: viewportElement,
          "data-reka-scroll-area-viewport": "",
          style: {
            /**
             * We don't support `visible` because the intention is to have at least one scrollbar
             * if this component is used and `visible` will behave like `auto` in that case
             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflowed#description
             *
             * We don't handle `auto` because the intention is for the native implementation
             * to be hidden if using this component. We just want to ensure the node is scrollable
             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent
             * the browser from having to work out whether to render native scrollbars or not,
             * we tell it to with the intention of hiding them in CSS.
             */
            overflowX: unref(rootContext).scrollbarXEnabled.value ? "scroll" : "hidden",
            overflowY: unref(rootContext).scrollbarYEnabled.value ? "scroll" : "hidden"
          }
        }, _ctx.$attrs, { tabindex: 0 }), [
          createVNode(unref(Primitive), {
            ref: unref(forwardRef),
            style: normalizeStyle({
              /**
               * When horizontal scrollbar is visible: this element should be at least
               * as wide as its children for size calculations to work correctly.
               *
               * When horizontal scrollbar is NOT visible: this element's width should
               * be constrained by the parent container to enable `text-overflow: ellipsis`
               */
              minWidth: unref(rootContext).scrollbarXEnabled.value ? "fit-content" : void 0
            }),
            "as-child": props.asChild,
            as: _ctx.as
          }, {
            default: withCtx(() => [
              renderSlot(_ctx.$slots, "default")
            ]),
            _: 3
          }, 8, ["style", "as-child", "as"])
        ], 16),
        createVNode(unref(Primitive), {
          as: "style",
          nonce: unref(nonce)
        }, {
          default: withCtx(() => _cache[0] || (_cache[0] = [
            createTextVNode(" /* Hide scrollbars cross-browser and enable momentum scroll for touch devices */ [data-reka-scroll-area-viewport] { scrollbar-width:none; -ms-overflow-style:none; -webkit-overflow-scrolling:touch; } [data-reka-scroll-area-viewport]::-webkit-scrollbar { display:none; } ")
          ])),
          _: 1
        }, 8, ["nonce"])
      ], 64);
    };
  }
});

export { _sfc_main as _ };
//# sourceMappingURL=ScrollAreaViewport.js.map
