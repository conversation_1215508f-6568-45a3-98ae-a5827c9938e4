{"version": 3, "file": "resizePanel.cjs", "sources": ["../../src/Splitter/utils/resizePanel.ts"], "sourcesContent": ["import type { PanelConstraints } from '../SplitterPanel.vue'\nimport { assert } from './assert'\nimport { fuzzyCompareNumbers } from './compare'\nimport { PRECISION } from './constants'\n\n// Panel size must be in percentages; pixel values should be pre-converted\nexport function resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size,\n}: {\n  panelConstraints: PanelConstraints[]\n  panelIndex: number\n  size: number\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex]\n  assert(panelConstraints != null)\n\n  const { collapsedSize = 0, collapsible, maxSize = 100, minSize = 0 } = panelConstraints\n\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0)\n        size = collapsedSize\n      else\n        size = minSize\n    }\n    else {\n      size = minSize\n    }\n  }\n\n  size = Math.min(maxSize, size)\n  size = Number.parseFloat(size.toFixed(PRECISION))\n\n  return size\n}\n"], "names": ["assert", "fuzzyCompareNumbers", "PRECISION"], "mappings": ";;;;;;AAMO,SAAS,WAAY,CAAA;AAAA,EAC1B,gBAAkB,EAAA,qBAAA;AAAA,EAClB,UAAA;AAAA,EACA;AACF,CAIG,EAAA;AACD,EAAM,MAAA,gBAAA,GAAmB,sBAAsB,UAAU,CAAA;AACzD,EAAAA,mBAAA,CAAO,oBAAoB,IAAI,CAAA;AAE/B,EAAM,MAAA,EAAE,gBAAgB,CAAG,EAAA,WAAA,EAAa,UAAU,GAAK,EAAA,OAAA,GAAU,GAAM,GAAA,gBAAA;AAEvE,EAAA,IAAIC,iCAAoB,CAAA,IAAA,EAAM,OAAO,CAAA,GAAI,CAAG,EAAA;AAC1C,IAAA,IAAI,WAAa,EAAA;AAEf,MAAM,MAAA,YAAA,GAAA,CAAgB,gBAAgB,OAAW,IAAA,CAAA;AACjD,MAAI,IAAAA,iCAAA,CAAoB,IAAM,EAAA,YAAY,CAAI,GAAA,CAAA;AAC5C,QAAO,IAAA,GAAA,aAAA;AAAA;AAEP,QAAO,IAAA,GAAA,OAAA;AAAA,KAEN,MAAA;AACH,MAAO,IAAA,GAAA,OAAA;AAAA;AACT;AAGF,EAAO,IAAA,GAAA,IAAA,CAAK,GAAI,CAAA,OAAA,EAAS,IAAI,CAAA;AAC7B,EAAA,IAAA,GAAO,MAAO,CAAA,UAAA,CAAW,IAAK,CAAA,OAAA,CAAQC,yBAAS,CAAC,CAAA;AAEhD,EAAO,OAAA,IAAA;AACT;;;;"}