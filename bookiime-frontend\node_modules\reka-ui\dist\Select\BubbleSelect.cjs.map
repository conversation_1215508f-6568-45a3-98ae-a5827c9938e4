{"version": 3, "file": "BubbleSelect.cjs", "sources": ["../../src/Select/BubbleSelect.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { ref, watch } from 'vue'\nimport { VisuallyHidden } from '@/VisuallyHidden'\n\ninterface BubbleSelectProps {\n  autocomplete?: string\n  autofocus?: boolean\n  disabled?: boolean\n  form?: string\n  multiple?: boolean\n  name?: string\n  required?: boolean\n  size?: number\n  value?: any\n}\n\nconst props = defineProps<BubbleSelectProps>()\nconst selectElement = ref<HTMLElement>()\n\n// This would bubble \"change\" event to form, with the target as Select element.\nwatch(() => props.value, (cur, prev) => {\n  const selectProto = window.HTMLSelectElement.prototype\n  const descriptor = Object.getOwnPropertyDescriptor(\n    selectProto,\n    'value',\n  ) as PropertyDescriptor\n  const setValue = descriptor.set\n  if (cur !== prev && setValue && selectElement.value) {\n    const event = new Event('change', { bubbles: true })\n    setValue.call(selectElement.value, cur)\n    selectElement.value.dispatchEvent(event)\n  }\n})\n\n/**\n * We purposefully use a `select` here to support form autofill as much\n * as possible.\n *\n * We purposefully do not add the `value` attribute here to allow the value\n * to be set programmatically and bubble to any parent form `onChange` event.\n *\n * We use `VisuallyHidden` rather than `display: \"none\"` because Safari autofill\n * won't work otherwise.\n */\n</script>\n\n<template>\n  <VisuallyHidden as-child>\n    <select\n      ref=\"selectElement\"\n      v-bind=\"props\"\n    >\n      <slot />\n    </select>\n  </VisuallyHidden>\n</template>\n"], "names": ["ref", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAA,MAAM,gBAAgBA,OAAiB,EAAA;AAGvC,IAAAC,SAAA,CAAM,MAAM,KAAA,CAAM,KAAO,EAAA,CAAC,KAAK,IAAS,KAAA;AACtC,MAAM,MAAA,WAAA,GAAc,OAAO,iBAAkB,CAAA,SAAA;AAC7C,MAAA,MAAM,aAAa,MAAO,CAAA,wBAAA;AAAA,QACxB,WAAA;AAAA,QACA;AAAA,OACF;AACA,MAAA,MAAM,WAAW,UAAW,CAAA,GAAA;AAC5B,MAAA,IAAI,GAAQ,KAAA,IAAA,IAAQ,QAAY,IAAA,aAAA,CAAc,KAAO,EAAA;AACnD,QAAA,MAAM,QAAQ,IAAI,KAAA,CAAM,UAAU,EAAE,OAAA,EAAS,MAAM,CAAA;AACnD,QAAS,QAAA,CAAA,IAAA,CAAK,aAAc,CAAA,KAAA,EAAO,GAAG,CAAA;AACtC,QAAc,aAAA,CAAA,KAAA,CAAM,cAAc,KAAK,CAAA;AAAA;AACzC,KACD,CAAA;;;;;;;;;;;;;;;;;;;"}