{"version": 3, "file": "TabsIndicator.cjs", "sources": ["../../src/Tabs/TabsIndicator.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { nextTick, ref, watch } from 'vue'\nimport { useForwardExpose } from '@/shared'\nimport { injectTabsRootContext } from './TabsRoot.vue'\n\nexport interface TabsIndicatorProps extends PrimitiveProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { useResizeObserver } from '@vueuse/core'\nimport { Primitive } from '@/Primitive'\n\nconst props = defineProps<TabsIndicatorProps>()\nconst context = injectTabsRootContext()\nuseForwardExpose()\n\ninterface IndicatorStyle {\n  size: number | null\n  position: number | null\n}\nconst activeTab = ref<HTMLElement | null>()\nconst indicatorStyle = ref<IndicatorStyle>({\n  size: null,\n  position: null,\n})\n\nwatch(() => [context.modelValue.value, context?.dir.value], async () => {\n  await nextTick()\n  updateIndicatorStyle()\n}, { immediate: true })\n\nuseResizeObserver([context.tabsList, activeTab], updateIndicatorStyle)\n\nfunction updateIndicatorStyle() {\n  activeTab.value = context.tabsList.value?.querySelector<HTMLButtonElement>('[role=\"tab\"][data-state=\"active\"]')\n\n  if (!activeTab.value)\n    return\n\n  if (context.orientation.value === 'horizontal') {\n    indicatorStyle.value = {\n      size: activeTab.value.offsetWidth,\n      position: activeTab.value.offsetLeft,\n    }\n  }\n  else {\n    indicatorStyle.value = {\n      size: activeTab.value.offsetHeight,\n      position: activeTab.value.offsetTop,\n    }\n  }\n}\n</script>\n\n<template>\n  <Primitive\n    v-if=\"typeof indicatorStyle.size === 'number'\"\n    v-bind=\"props\"\n    :style=\"{\n      '--reka-tabs-indicator-size': `${indicatorStyle.size}px`,\n      '--reka-tabs-indicator-position': `${indicatorStyle.position}px`,\n    }\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": ["injectTabsRootContext", "useForwardExpose", "ref", "watch", "nextTick", "useResizeObserver"], "mappings": ";;;;;;;;;;;;;;;AAaA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAA,MAAM,UAAUA,mCAAsB,EAAA;AACtC,IAAiBC,wCAAA,EAAA;AAMjB,IAAA,MAAM,YAAYC,OAAwB,EAAA;AAC1C,IAAA,MAAM,iBAAiBA,OAAoB,CAAA;AAAA,MACzC,IAAM,EAAA,IAAA;AAAA,MACN,QAAU,EAAA;AAAA,KACX,CAAA;AAED,IAAMC,SAAA,CAAA,MAAM,CAAC,OAAQ,CAAA,UAAA,CAAW,OAAO,OAAS,EAAA,GAAA,CAAI,KAAK,CAAA,EAAG,YAAY;AACtE,MAAA,MAAMC,YAAS,EAAA;AACf,MAAqB,oBAAA,EAAA;AAAA,KACpB,EAAA,EAAE,SAAW,EAAA,IAAA,EAAM,CAAA;AAEtB,IAAAC,sBAAA,CAAkB,CAAC,OAAA,CAAQ,QAAU,EAAA,SAAS,GAAG,oBAAoB,CAAA;AAErE,IAAA,SAAS,oBAAuB,GAAA;AAC9B,MAAA,SAAA,CAAU,KAAQ,GAAA,OAAA,CAAQ,QAAS,CAAA,KAAA,EAAO,cAAiC,mCAAmC,CAAA;AAE9G,MAAA,IAAI,CAAC,SAAU,CAAA,KAAA;AACb,QAAA;AAEF,MAAI,IAAA,OAAA,CAAQ,WAAY,CAAA,KAAA,KAAU,YAAc,EAAA;AAC9C,QAAA,cAAA,CAAe,KAAQ,GAAA;AAAA,UACrB,IAAA,EAAM,UAAU,KAAM,CAAA,WAAA;AAAA,UACtB,QAAA,EAAU,UAAU,KAAM,CAAA;AAAA,SAC5B;AAAA,OAEG,MAAA;AACH,QAAA,cAAA,CAAe,KAAQ,GAAA;AAAA,UACrB,IAAA,EAAM,UAAU,KAAM,CAAA,YAAA;AAAA,UACtB,QAAA,EAAU,UAAU,KAAM,CAAA;AAAA,SAC5B;AAAA;AACF;;;;;;;;;;;;;;;;;;;"}