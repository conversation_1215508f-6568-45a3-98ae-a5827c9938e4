{"version": 3, "file": "utils.cjs", "sources": ["../../src/ScrollArea/utils.ts"], "sourcesContent": ["import type { Direction, Sizes } from './types'\nimport { clamp } from '@/shared'\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(\n  input: readonly [number, number],\n  output: readonly [number, number],\n) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1])\n      return output[0]\n    const ratio = (output[1] - output[0]) / (input[1] - input[0])\n    return output[0] + ratio * (value - input[0])\n  }\n}\n\nexport function getThumbSize(sizes: Sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content)\n  const scrollbarPadding\n    = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio\n  // minimum of 18 matches macOS minimum\n  return Math.max(thumbSize, 18)\n}\n\nexport function getThumbRatio(viewportSize: number, contentSize: number) {\n  const ratio = viewportSize / contentSize\n  return Number.isNaN(ratio) ? 0 : ratio\n}\n\n// Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nexport function addUnlinkedScrollListener(\n  node: HTMLElement,\n  handler = () => {},\n) {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop }\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop }\n    const isHorizontalScroll = prevPosition.left !== position.left\n    const isVerticalScroll = prevPosition.top !== position.top\n    if (isHorizontalScroll || isVerticalScroll)\n      handler()\n    prevPosition = position\n    rAF = window.requestAnimationFrame(loop)\n  })()\n  return () => window.cancelAnimationFrame(rAF)\n}\n\nexport function getThumbOffsetFromScroll(\n  scrollPos: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr',\n) {\n  const thumbSizePx = getThumbSize(sizes)\n  const scrollbarPadding\n    = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding\n  const maxScrollPos = sizes.content - sizes.viewport\n  const maxThumbPos = scrollbar - thumbSizePx\n  const scrollClampRange\n    = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0]\n  const scrollWithoutMomentum = clamp(\n    scrollPos,\n    scrollClampRange[0],\n    scrollClampRange[1],\n  )\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos])\n  return interpolate(scrollWithoutMomentum)\n}\n\nexport function toInt(value?: string) {\n  return value ? Number.parseInt(value, 10) : 0\n}\n\nexport function getScrollPositionFromPointer(\n  pointerPos: number,\n  pointerOffset: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr',\n) {\n  const thumbSizePx = getThumbSize(sizes)\n  const thumbCenter = thumbSizePx / 2\n  const offset = pointerOffset || thumbCenter\n  const thumbOffsetFromEnd = thumbSizePx - offset\n  const minPointerPos = sizes.scrollbar.paddingStart + offset\n  const maxPointerPos\n    = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd\n  const maxScrollPos = sizes.content - sizes.viewport\n  const scrollRange\n    = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0]\n  const interpolate = linearScale(\n    [minPointerPos, maxPointerPos],\n    scrollRange as [number, number],\n  )\n  return interpolate(pointerPos)\n}\n\nexport function isScrollingWithinScrollbarBounds(\n  scrollPos: number,\n  maxScrollPos: number,\n) {\n  return scrollPos > 0 && scrollPos < maxScrollPos\n}\n"], "names": ["clamp"], "mappings": ";;;;AAIA,SAAS,WAAA,CACP,OACA,MACA,EAAA;AACA,EAAA,OAAO,CAAC,KAAkB,KAAA;AACxB,IAAI,IAAA,KAAA,CAAM,CAAC,CAAA,KAAM,KAAM,CAAA,CAAC,KAAK,MAAO,CAAA,CAAC,CAAM,KAAA,MAAA,CAAO,CAAC,CAAA;AACjD,MAAA,OAAO,OAAO,CAAC,CAAA;AACjB,IAAM,MAAA,KAAA,GAAA,CAAS,MAAO,CAAA,CAAC,CAAI,GAAA,MAAA,CAAO,CAAC,CAAA,KAAM,KAAM,CAAA,CAAC,CAAI,GAAA,KAAA,CAAM,CAAC,CAAA,CAAA;AAC3D,IAAA,OAAO,OAAO,CAAC,CAAA,GAAI,KAAS,IAAA,KAAA,GAAQ,MAAM,CAAC,CAAA,CAAA;AAAA,GAC7C;AACF;AAEO,SAAS,aAAa,KAAc,EAAA;AACzC,EAAA,MAAM,KAAQ,GAAA,aAAA,CAAc,KAAM,CAAA,QAAA,EAAU,MAAM,OAAO,CAAA;AACzD,EAAA,MAAM,gBACF,GAAA,KAAA,CAAM,SAAU,CAAA,YAAA,GAAe,MAAM,SAAU,CAAA,UAAA;AACnD,EAAA,MAAM,SAAa,GAAA,CAAA,KAAA,CAAM,SAAU,CAAA,IAAA,GAAO,gBAAoB,IAAA,KAAA;AAE9D,EAAO,OAAA,IAAA,CAAK,GAAI,CAAA,SAAA,EAAW,EAAE,CAAA;AAC/B;AAEgB,SAAA,aAAA,CAAc,cAAsB,WAAqB,EAAA;AACvE,EAAA,MAAM,QAAQ,YAAe,GAAA,WAAA;AAC7B,EAAA,OAAO,MAAO,CAAA,KAAA,CAAM,KAAK,CAAA,GAAI,CAAI,GAAA,KAAA;AACnC;AAIgB,SAAA,yBAAA,CACd,IACA,EAAA,OAAA,GAAU,MAAM;AAAC,CACjB,EAAA;AACA,EAAA,IAAI,eAAe,EAAE,IAAA,EAAM,KAAK,UAAY,EAAA,GAAA,EAAK,KAAK,SAAU,EAAA;AAChE,EAAA,IAAI,GAAM,GAAA,CAAA;AACV,EAAA,CAAC,SAAS,IAAO,GAAA;AACf,IAAA,MAAM,WAAW,EAAE,IAAA,EAAM,KAAK,UAAY,EAAA,GAAA,EAAK,KAAK,SAAU,EAAA;AAC9D,IAAM,MAAA,kBAAA,GAAqB,YAAa,CAAA,IAAA,KAAS,QAAS,CAAA,IAAA;AAC1D,IAAM,MAAA,gBAAA,GAAmB,YAAa,CAAA,GAAA,KAAQ,QAAS,CAAA,GAAA;AACvD,IAAA,IAAI,kBAAsB,IAAA,gBAAA;AACxB,MAAQ,OAAA,EAAA;AACV,IAAe,YAAA,GAAA,QAAA;AACf,IAAM,GAAA,GAAA,MAAA,CAAO,sBAAsB,IAAI,CAAA;AAAA,GACtC,GAAA;AACH,EAAO,OAAA,MAAM,MAAO,CAAA,oBAAA,CAAqB,GAAG,CAAA;AAC9C;AAEO,SAAS,wBACd,CAAA,SAAA,EACA,KACA,EAAA,GAAA,GAAiB,KACjB,EAAA;AACA,EAAM,MAAA,WAAA,GAAc,aAAa,KAAK,CAAA;AACtC,EAAA,MAAM,gBACF,GAAA,KAAA,CAAM,SAAU,CAAA,YAAA,GAAe,MAAM,SAAU,CAAA,UAAA;AACnD,EAAM,MAAA,SAAA,GAAY,KAAM,CAAA,SAAA,CAAU,IAAO,GAAA,gBAAA;AACzC,EAAM,MAAA,YAAA,GAAe,KAAM,CAAA,OAAA,GAAU,KAAM,CAAA,QAAA;AAC3C,EAAA,MAAM,cAAc,SAAY,GAAA,WAAA;AAChC,EAAM,MAAA,gBAAA,GACF,GAAQ,KAAA,KAAA,GAAQ,CAAC,CAAA,EAAG,YAAY,CAAI,GAAA,CAAC,YAAe,GAAA,EAAA,EAAI,CAAC,CAAA;AAC7D,EAAA,MAAM,qBAAwB,GAAAA,kBAAA;AAAA,IAC5B,SAAA;AAAA,IACA,iBAAiB,CAAC,CAAA;AAAA,IAClB,iBAAiB,CAAC;AAAA,GACpB;AACA,EAAM,MAAA,WAAA,GAAc,YAAY,CAAC,CAAA,EAAG,YAAY,CAAG,EAAA,CAAC,CAAG,EAAA,WAAW,CAAC,CAAA;AACnE,EAAA,OAAO,YAAY,qBAAqB,CAAA;AAC1C;AAEO,SAAS,MAAM,KAAgB,EAAA;AACpC,EAAA,OAAO,KAAQ,GAAA,MAAA,CAAO,QAAS,CAAA,KAAA,EAAO,EAAE,CAAI,GAAA,CAAA;AAC9C;AAEO,SAAS,4BACd,CAAA,UAAA,EACA,aACA,EAAA,KAAA,EACA,MAAiB,KACjB,EAAA;AACA,EAAM,MAAA,WAAA,GAAc,aAAa,KAAK,CAAA;AACtC,EAAA,MAAM,cAAc,WAAc,GAAA,CAAA;AAClC,EAAA,MAAM,SAAS,aAAiB,IAAA,WAAA;AAChC,EAAA,MAAM,qBAAqB,WAAc,GAAA,MAAA;AACzC,EAAM,MAAA,aAAA,GAAgB,KAAM,CAAA,SAAA,CAAU,YAAe,GAAA,MAAA;AACrD,EAAA,MAAM,gBACF,KAAM,CAAA,SAAA,CAAU,IAAO,GAAA,KAAA,CAAM,UAAU,UAAa,GAAA,kBAAA;AACxD,EAAM,MAAA,YAAA,GAAe,KAAM,CAAA,OAAA,GAAU,KAAM,CAAA,QAAA;AAC3C,EAAM,MAAA,WAAA,GACF,GAAQ,KAAA,KAAA,GAAQ,CAAC,CAAA,EAAG,YAAY,CAAI,GAAA,CAAC,YAAe,GAAA,EAAA,EAAI,CAAC,CAAA;AAC7D,EAAA,MAAM,WAAc,GAAA,WAAA;AAAA,IAClB,CAAC,eAAe,aAAa,CAAA;AAAA,IAC7B;AAAA,GACF;AACA,EAAA,OAAO,YAAY,UAAU,CAAA;AAC/B;AAEgB,SAAA,gCAAA,CACd,WACA,YACA,EAAA;AACA,EAAO,OAAA,SAAA,GAAY,KAAK,SAAY,GAAA,YAAA;AACtC;;;;;;;;;;"}