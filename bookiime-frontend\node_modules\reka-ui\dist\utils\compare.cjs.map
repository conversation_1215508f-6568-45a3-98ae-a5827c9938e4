{"version": 3, "file": "compare.cjs", "sources": ["../../src/Splitter/utils/compare.ts"], "sourcesContent": ["import { PRECISION } from './constants'\n\nexport function fuzzyCompareNumbers(\n  actual: number,\n  expected: number,\n  fractionDigits: number = PRECISION,\n): number {\n  actual = Number.parseFloat(actual.toFixed(fractionDigits))\n  expected = Number.parseFloat(expected.toFixed(fractionDigits))\n\n  const delta = actual - expected\n  if (delta === 0)\n    return 0\n  else\n    return delta > 0 ? 1 : -1\n}\n\nexport function fuzzyNumbersEqual(\n  actual: number,\n  expected: number,\n  fractionDigits?: number,\n): boolean {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0\n}\n"], "names": ["PRECISION"], "mappings": ";;;;AAEO,SAAS,mBACd,CAAA,MAAA,EACA,QACA,EAAA,cAAA,GAAyBA,yBACjB,EAAA;AACR,EAAA,MAAA,GAAS,MAAO,CAAA,UAAA,CAAW,MAAO,CAAA,OAAA,CAAQ,cAAc,CAAC,CAAA;AACzD,EAAA,QAAA,GAAW,MAAO,CAAA,UAAA,CAAW,QAAS,CAAA,OAAA,CAAQ,cAAc,CAAC,CAAA;AAE7D,EAAA,MAAM,QAAQ,MAAS,GAAA,QAAA;AACvB,EAAA,IAAI,KAAU,KAAA,CAAA;AACZ,IAAO,OAAA,CAAA;AAAA;AAEP,IAAO,OAAA,KAAA,GAAQ,IAAI,CAAI,GAAA,EAAA;AAC3B;AAEgB,SAAA,iBAAA,CACd,MACA,EAAA,QAAA,EACA,cACS,EAAA;AACT,EAAA,OAAO,mBAAoB,CAAA,MAAA,EAAQ,QAAU,EAAA,cAAc,CAAM,KAAA,CAAA;AACnE;;;;;"}