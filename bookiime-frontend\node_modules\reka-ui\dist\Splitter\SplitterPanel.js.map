{"version": 3, "file": "SplitterPanel.js", "sources": ["../../src/Splitter/SplitterPanel.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useId } from '@/shared'\nimport { PRECISION } from './utils/constants'\n\nexport interface SplitterPanelProps extends PrimitiveProps {\n  /** The size of panel when it is collapsed. */\n  collapsedSize?: number\n  /** Should panel collapse when resized beyond its `minSize`. When `true`, it will be collapsed to `collapsedSize`. */\n  collapsible?: boolean\n  /** Initial size of panel (numeric value between 1-100) */\n  defaultSize?: number\n  /** Panel id (unique within group); falls back to `useId` when not provided */\n  id?: string\n  /** The maximum allowable size of panel (numeric value between 1-100); defaults to `100` */\n  maxSize?: number\n  /** The minimum allowable size of panel (numeric value between 1-100); defaults to `10` */\n  minSize?: number\n  /** The order of panel within group; required for groups with conditionally rendered panels */\n  order?: number\n}\n\nexport type SplitterPanelEmits = {\n  /** Event handler called when panel is collapsed. */\n  collapse: []\n  /** Event handler called when panel is expanded. */\n  expand: []\n  /** Event handler called when panel is resized; size parameter is a numeric value between 1-100.  */\n  resize: [size: number, prevSize: number | undefined]\n}\n\nexport type PanelOnCollapse = () => void\nexport type PanelOnExpand = () => void\nexport type PanelOnResize = (\n  size: number,\n  prevSize: number | undefined\n) => void\n\nexport type PanelCallbacks = {\n  onCollapse?: PanelOnCollapse\n  onExpand?: PanelOnExpand\n  onResize?: PanelOnResize\n}\n\nexport type PanelConstraints = {\n  collapsedSize?: number | undefined\n  collapsible?: boolean | undefined\n  defaultSize?: number | undefined\n  /** Panel id (unique within group); falls back to useId when not provided */\n  maxSize?: number | undefined\n  minSize?: number | undefined\n}\n\nexport type PanelData = {\n  callbacks: PanelCallbacks\n  constraints: PanelConstraints\n  id: string\n  idIsFromProps: boolean\n  order: number | undefined\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { computed, onMounted, onUnmounted, watch } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { injectPanelGroupContext } from './SplitterGroup.vue'\n\nconst props = defineProps<SplitterPanelProps>()\nconst emits = defineEmits<SplitterPanelEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Is the panel collapsed */\n    isCollapsed: typeof isCollapsed.value\n    /** Is the panel expanded */\n    isExpanded: typeof isExpanded.value\n    /** If panel is `collapsible`, collapse it fully. */\n    collapse: typeof collapse\n    /** If panel is currently collapsed, expand it to its most recent size. */\n    expand: typeof expand\n    /** Resize panel to the specified percentage (1 - 100). */\n    resize: typeof resize\n  }) => any\n}>()\n\nconst panelGroupContext = injectPanelGroupContext()\nif (panelGroupContext === null) {\n  throw new Error(\n    'SplitterPanel components must be rendered within a SplitterGroup container',\n  )\n}\n\nconst { collapsePanel, expandPanel, getPanelSize, getPanelStyle, isPanelCollapsed, resizePanel, groupId, reevaluatePanelConstraints, registerPanel, unregisterPanel } = panelGroupContext\nconst panelId = useId(props.id, 'reka-splitter-panel')\n\nconst panelDataRef = computed(() => ({\n  callbacks: {\n    onCollapse: () => emits('collapse'),\n    onExpand: () => emits('expand'),\n    onResize: (...args) => emits('resize', ...args),\n  },\n  constraints: {\n    collapsedSize: props.collapsedSize && Number.parseFloat(props.collapsedSize.toFixed(PRECISION)),\n    collapsible: props.collapsible,\n    defaultSize: props.defaultSize,\n    /** Panel id (unique within group); falls back to useId when not provided */\n    /** Panel id (unique within group); falls back to useId when not provided */\n    maxSize: props.maxSize,\n    minSize: props.minSize,\n  },\n  id: panelId,\n  idIsFromProps: props.id !== undefined,\n  order: props.order,\n}) satisfies PanelData)\n\nwatch(() => panelDataRef.value.constraints, (constraints, prevConstraints) => {\n  // If constraints have changed, we should revisit panel sizes.\n  // This is uncommon but may happen if people are trying to implement pixel based constraints.\n  if (\n    prevConstraints.collapsedSize !== constraints.collapsedSize\n    || prevConstraints.collapsible !== constraints.collapsible\n    || prevConstraints.maxSize !== constraints.maxSize\n    || prevConstraints.minSize !== constraints.minSize\n  ) {\n    reevaluatePanelConstraints(panelDataRef.value, prevConstraints)\n  }\n}, { deep: true })\n\nonMounted(() => {\n  const panelData = panelDataRef.value\n  registerPanel(panelData)\n  onUnmounted(() => {\n    unregisterPanel(panelData)\n  })\n})\n\nconst style = computed(() => getPanelStyle(panelDataRef.value, props.defaultSize))\n/** Panel id (unique within group); falls back to useId when not provided */\n\nconst isCollapsed = computed(() => isPanelCollapsed(panelDataRef.value))\nconst isExpanded = computed(() => !isCollapsed.value)\n\nfunction collapse() {\n  collapsePanel(panelDataRef.value)\n}\n\nfunction expand() {\n  expandPanel(panelDataRef.value)\n}\n\nfunction resize(size: number) {\n  resizePanel(panelDataRef.value, size)\n}\n\ndefineExpose({\n  /** If panel is `collapsible`, collapse it fully. */\n  collapse,\n  /** If panel is currently collapsed, expand it to its most recent size. */\n  expand,\n  /** Gets the current size of the panel as a percentage (1 - 100). */\n  getSize() {\n    return getPanelSize(panelDataRef.value)\n  },\n  /** Resize panel to the specified percentage (1 - 100). */\n  resize,\n  /** Returns `true` if the panel is currently collapsed */\n  isCollapsed,\n  /** Returns `true` if the panel is currently not collapsed */\n  isExpanded,\n})\n</script>\n\n<template>\n  <Primitive\n    :id=\"panelId\"\n    :style=\"style\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    data-panel=\"\"\n    :data-panel-collapsible=\"collapsible || undefined\"\n    :data-panel-group-id=\"groupId\"\n    :data-panel-id=\"panelId\"\n    :data-panel-size=\" Number.parseFloat(`${style.flexGrow}`).toFixed(1)\"\n    :data-state=\"collapsible ? isCollapsed ? 'collapsed' : 'expanded' : undefined\"\n  >\n    <slot\n      :is-collapsed=\"isCollapsed\"\n      :is-expanded=\"isExpanded\"\n      :expand=\"expand\"\n      :collapse=\"collapse\"\n      :resize=\"resize\"\n    />\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmEA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAA,MAAM,KAAQ,GAAA,MAAA;AAiBd,IAAA,MAAM,oBAAoB,uBAAwB,EAAA;AAClD,IAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,MAAA,MAAM,IAAI,KAAA;AAAA,QACR;AAAA,OACF;AAAA;AAGF,IAAM,MAAA,EAAE,aAAe,EAAA,WAAA,EAAa,YAAc,EAAA,aAAA,EAAe,gBAAkB,EAAA,WAAA,EAAa,OAAS,EAAA,0BAAA,EAA4B,aAAe,EAAA,eAAA,EAAoB,GAAA,iBAAA;AACxK,IAAA,MAAM,OAAU,GAAA,KAAA,CAAM,KAAM,CAAA,EAAA,EAAI,qBAAqB,CAAA;AAErD,IAAM,MAAA,YAAA,GAAe,SAAS,OAAO;AAAA,MACnC,SAAW,EAAA;AAAA,QACT,UAAA,EAAY,MAAM,KAAA,CAAM,UAAU,CAAA;AAAA,QAClC,QAAA,EAAU,MAAM,KAAA,CAAM,QAAQ,CAAA;AAAA,QAC9B,UAAU,CAAI,GAAA,IAAA,KAAS,KAAM,CAAA,QAAA,EAAU,GAAG,IAAI;AAAA,OAChD;AAAA,MACA,WAAa,EAAA;AAAA,QACX,aAAA,EAAe,MAAM,aAAiB,IAAA,MAAA,CAAO,WAAW,KAAM,CAAA,aAAA,CAAc,OAAQ,CAAA,SAAS,CAAC,CAAA;AAAA,QAC9F,aAAa,KAAM,CAAA,WAAA;AAAA,QACnB,aAAa,KAAM,CAAA,WAAA;AAAA;AAAA;AAAA,QAGnB,SAAS,KAAM,CAAA,OAAA;AAAA,QACf,SAAS,KAAM,CAAA;AAAA,OACjB;AAAA,MACA,EAAI,EAAA,OAAA;AAAA,MACJ,aAAA,EAAe,MAAM,EAAO,KAAA,MAAA;AAAA,MAC5B,OAAO,KAAM,CAAA;AAAA,KACO,CAAA,CAAA;AAEtB,IAAA,KAAA,CAAM,MAAM,YAAa,CAAA,KAAA,CAAM,WAAa,EAAA,CAAC,aAAa,eAAoB,KAAA;AAG5E,MAAA,IACE,eAAgB,CAAA,aAAA,KAAkB,WAAY,CAAA,aAAA,IAC3C,gBAAgB,WAAgB,KAAA,WAAA,CAAY,WAC5C,IAAA,eAAA,CAAgB,YAAY,WAAY,CAAA,OAAA,IACxC,eAAgB,CAAA,OAAA,KAAY,YAAY,OAC3C,EAAA;AACA,QAA2B,0BAAA,CAAA,YAAA,CAAa,OAAO,eAAe,CAAA;AAAA;AAChE,KACC,EAAA,EAAE,IAAM,EAAA,IAAA,EAAM,CAAA;AAEjB,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,MAAM,YAAY,YAAa,CAAA,KAAA;AAC/B,MAAA,aAAA,CAAc,SAAS,CAAA;AACvB,MAAA,WAAA,CAAY,MAAM;AAChB,QAAA,eAAA,CAAgB,SAAS,CAAA;AAAA,OAC1B,CAAA;AAAA,KACF,CAAA;AAED,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM,aAAA,CAAc,aAAa,KAAO,EAAA,KAAA,CAAM,WAAW,CAAC,CAAA;AAGjF,IAAA,MAAM,cAAc,QAAS,CAAA,MAAM,gBAAiB,CAAA,YAAA,CAAa,KAAK,CAAC,CAAA;AACvE,IAAA,MAAM,UAAa,GAAA,QAAA,CAAS,MAAM,CAAC,YAAY,KAAK,CAAA;AAEpD,IAAA,SAAS,QAAW,GAAA;AAClB,MAAA,aAAA,CAAc,aAAa,KAAK,CAAA;AAAA;AAGlC,IAAA,SAAS,MAAS,GAAA;AAChB,MAAA,WAAA,CAAY,aAAa,KAAK,CAAA;AAAA;AAGhC,IAAA,SAAS,OAAO,IAAc,EAAA;AAC5B,MAAY,WAAA,CAAA,YAAA,CAAa,OAAO,IAAI,CAAA;AAAA;AAGtC,IAAa,QAAA,CAAA;AAAA;AAAA,MAEX,QAAA;AAAA;AAAA,MAEA,MAAA;AAAA;AAAA,MAEA,OAAU,GAAA;AACR,QAAO,OAAA,YAAA,CAAa,aAAa,KAAK,CAAA;AAAA,OACxC;AAAA;AAAA,MAEA,MAAA;AAAA;AAAA,MAEA,WAAA;AAAA;AAAA,MAEA;AAAA,KACD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}