export declare const errors: {
    'unable-to-get-rootdir': string;
    'no-extension': string;
    'invalid-file-manifest-name': string;
    'unable-to-get-file-manifest-name': string;
    'invalid-sw-dest': string;
    'unable-to-get-sw-name': string;
    'unable-to-get-save-config': string;
    'unable-to-get-file-hash': string;
    'unable-to-get-file-size': string;
    'unable-to-glob-files': string;
    'unable-to-make-manifest-directory': string;
    'read-manifest-template-failure': string;
    'populating-manifest-tmpl-failed': string;
    'manifest-file-write-failure': string;
    'unable-to-make-sw-directory': string;
    'read-sw-template-failure': string;
    'sw-write-failure': string;
    'sw-write-failure-directory': string;
    'unable-to-copy-workbox-libraries': string;
    'invalid-generate-sw-input': string;
    'invalid-glob-directory': string;
    'invalid-dont-cache-bust': string;
    'invalid-exclude-files': string;
    'invalid-get-manifest-entries-input': string;
    'invalid-manifest-path': string;
    'invalid-manifest-entries': string;
    'invalid-manifest-format': string;
    'invalid-static-file-globs': string;
    'invalid-templated-urls': string;
    'templated-url-matches-glob': string;
    'invalid-glob-ignores': string;
    'manifest-entry-bad-url': string;
    'modify-url-prefix-bad-prefixes': string;
    'invalid-inject-manifest-arg': string;
    'injection-point-not-found': string;
    'multiple-injection-points': string;
    'populating-sw-tmpl-failed': string;
    'useless-glob-pattern': string;
    'bad-template-urls-asset': string;
    'invalid-runtime-caching': string;
    'static-file-globs-deprecated': string;
    'dynamic-url-deprecated': string;
    'urlPattern-is-required': string;
    'handler-is-required': string;
    'invalid-generate-file-manifest-arg': string;
    'invalid-sw-src': string;
    'same-src-and-dest': string;
    'only-regexp-routes-supported': string;
    'bad-runtime-caching-config': string;
    'invalid-network-timeout-seconds': string;
    'no-module-name': string;
    'bad-manifest-transforms-return-value': string;
    'string-entry-warning': string;
    'no-manifest-entries-or-runtime-caching': string;
    'cant-find-sourcemap': string;
    'nav-preload-runtime-caching': string;
    'cache-name-required': string;
    'manifest-transforms': string;
    'invalid-handler-string': string;
};
