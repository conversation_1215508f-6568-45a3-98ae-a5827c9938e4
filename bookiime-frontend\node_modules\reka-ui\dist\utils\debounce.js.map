{"version": 3, "file": "debounce.js", "sources": ["../../src/Splitter/utils/debounce.ts"], "sourcesContent": ["export default function debounce<T extends Function>(\n  callback: T,\n  durationMs = 10,\n) {\n  let timeoutId: NodeJS.Timeout | null = null\n\n  const callable = (...args: any) => {\n    if (timeoutId !== null)\n      clearTimeout(timeoutId)\n\n    timeoutId = setTimeout(() => {\n      callback(...args)\n    }, durationMs)\n  }\n\n  return callable as unknown as T\n}\n"], "names": [], "mappings": "AAAwB,SAAA,QAAA,CACtB,QACA,EAAA,UAAA,GAAa,EACb,EAAA;AACA,EAAA,IAAI,SAAmC,GAAA,IAAA;AAEvC,EAAM,MAAA,QAAA,GAAW,IAAI,IAAc,KAAA;AACjC,IAAA,IAAI,SAAc,KAAA,IAAA;AAChB,MAAA,YAAA,CAAa,SAAS,CAAA;AAExB,IAAA,SAAA,GAAY,WAAW,MAAM;AAC3B,MAAA,QAAA,CAAS,GAAG,IAAI,CAAA;AAAA,OACf,UAAU,CAAA;AAAA,GACf;AAEA,EAAO,OAAA,QAAA;AACT;;;;"}