{"version": 3, "file": "dom.cjs", "sources": ["../../src/Splitter/utils/dom.ts"], "sourcesContent": ["import type { PanelData } from '../SplitterPanel.vue'\nimport { isBrowser } from '@/shared'\n\nexport function getPanelElement(\n  id: string,\n  scope: ParentNode | HTMLElement = document,\n): HTMLElement | null {\n  if (!isBrowser)\n    return null\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`)\n  if (element)\n    return element as HTMLElement\n\n  return null\n}\n\nexport function getPanelElementsForGroup(\n  groupId: string,\n  scope: ParentNode | HTMLElement = document,\n): HTMLElement[] {\n  if (!isBrowser)\n    return []\n  return Array.from(\n    scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`),\n  )\n}\n\nexport function getPanelGroupElement(\n  id: string,\n  rootElement: ParentNode | HTMLElement = document,\n): HTMLElement | null {\n  if (!isBrowser)\n    return null\n  // If the root element is the PanelGroup\n  if (\n    rootElement instanceof HTMLElement\n    && (rootElement as HTMLElement)?.dataset?.panelGroupId === id\n  ) {\n    return rootElement as HTMLElement\n  }\n\n  // Else query children\n  const element = rootElement.querySelector(\n    `[data-panel-group][data-panel-group-id=\"${id}\"]`,\n  )\n  if (element)\n    return element as HTMLElement\n\n  return null\n}\n\nexport function getResizeHandleElement(\n  id: string,\n  scope: ParentNode | HTMLElement = document,\n): HTMLElement | null {\n  if (!isBrowser)\n    return null\n  const element = scope.querySelector(`[data-panel-resize-handle-id=\"${id}\"]`)\n  if (element)\n    return element as HTMLElement\n\n  return null\n}\n\nexport function getResizeHandleElementIndex(\n  groupId: string,\n  id: string,\n  scope: ParentNode | HTMLElement = document,\n): number | null {\n  if (!isBrowser)\n    return null\n  const handles = getResizeHandleElementsForGroup(groupId, scope)\n  const index = handles.findIndex(\n    handle => handle.getAttribute('data-panel-resize-handle-id') === id,\n  )\n  return index ?? null\n}\n\nexport function getResizeHandleElementsForGroup(\n  groupId: string,\n  scope: ParentNode | HTMLElement = document,\n): HTMLElement[] {\n  if (!isBrowser)\n    return []\n  return Array.from(\n    scope.querySelectorAll(\n      `[data-panel-resize-handle-id][data-panel-group-id=\"${groupId}\"]`,\n    ),\n  )\n}\n\nexport function getResizeHandlePanelIds(\n  groupId: string,\n  handleId: string,\n  panelsArray: PanelData[],\n  scope: ParentNode | HTMLElement = document,\n): [idBefore: string | null, idAfter: string | null] {\n  const handle = getResizeHandleElement(handleId, scope)\n  const handles = getResizeHandleElementsForGroup(groupId, scope)\n  const index = handle ? handles.indexOf(handle) : -1\n\n  const idBefore: string | null = panelsArray[index]?.id ?? null\n  const idAfter: string | null = panelsArray[index + 1]?.id ?? null\n\n  return [idBefore, idAfter]\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AA2BgB,SAAA,oBAAA,CACd,EACA,EAAA,WAAA,GAAwC,QACpB,EAAA;AACpB,EAAA,IAAI,CAACA,wBAAA;AACH,IAAO,OAAA,IAAA;AAET,EAAA,IACE,WAAuB,YAAA,WAAA,IACnB,WAA6B,EAAA,OAAA,EAAS,iBAAiB,EAC3D,EAAA;AACA,IAAO,OAAA,WAAA;AAAA;AAIT,EAAA,MAAM,UAAU,WAAY,CAAA,aAAA;AAAA,IAC1B,2CAA2C,EAAE,CAAA,EAAA;AAAA,GAC/C;AACA,EAAI,IAAA,OAAA;AACF,IAAO,OAAA,OAAA;AAET,EAAO,OAAA,IAAA;AACT;AAEgB,SAAA,sBAAA,CACd,EACA,EAAA,KAAA,GAAkC,QACd,EAAA;AACpB,EAAA,IAAI,CAACA,wBAAA;AACH,IAAO,OAAA,IAAA;AACT,EAAA,MAAM,OAAU,GAAA,KAAA,CAAM,aAAc,CAAA,CAAA,8BAAA,EAAiC,EAAE,CAAI,EAAA,CAAA,CAAA;AAC3E,EAAI,IAAA,OAAA;AACF,IAAO,OAAA,OAAA;AAET,EAAO,OAAA,IAAA;AACT;AAEO,SAAS,2BACd,CAAA,OAAA,EACA,EACA,EAAA,KAAA,GAAkC,QACnB,EAAA;AACf,EAAA,IAAI,CAACA,wBAAA;AACH,IAAO,OAAA,IAAA;AACT,EAAM,MAAA,OAAA,GAAU,+BAAgC,CAAA,OAAA,EAAS,KAAK,CAAA;AAC9D,EAAA,MAAM,QAAQ,OAAQ,CAAA,SAAA;AAAA,IACpB,CAAU,MAAA,KAAA,MAAA,CAAO,YAAa,CAAA,6BAA6B,CAAM,KAAA;AAAA,GACnE;AACA,EAAA,OAAO,KAAS,IAAA,IAAA;AAClB;AAEgB,SAAA,+BAAA,CACd,OACA,EAAA,KAAA,GAAkC,QACnB,EAAA;AACf,EAAA,IAAI,CAACA,wBAAA;AACH,IAAA,OAAO,EAAC;AACV,EAAA,OAAO,KAAM,CAAA,IAAA;AAAA,IACX,KAAM,CAAA,gBAAA;AAAA,MACJ,sDAAsD,OAAO,CAAA,EAAA;AAAA;AAC/D,GACF;AACF;AAEO,SAAS,uBACd,CAAA,OAAA,EACA,QACA,EAAA,WAAA,EACA,QAAkC,QACiB,EAAA;AACnD,EAAM,MAAA,MAAA,GAAS,sBAAuB,CAAA,QAAA,EAAU,KAAK,CAAA;AACrD,EAAM,MAAA,OAAA,GAAU,+BAAgC,CAAA,OAAA,EAAS,KAAK,CAAA;AAC9D,EAAA,MAAM,KAAQ,GAAA,MAAA,GAAS,OAAQ,CAAA,OAAA,CAAQ,MAAM,CAAI,GAAA,EAAA;AAEjD,EAAA,MAAM,QAA0B,GAAA,WAAA,CAAY,KAAK,CAAA,EAAG,EAAM,IAAA,IAAA;AAC1D,EAAA,MAAM,OAAyB,GAAA,WAAA,CAAY,KAAQ,GAAA,CAAC,GAAG,EAAM,IAAA,IAAA;AAE7D,EAAO,OAAA,CAAC,UAAU,OAAO,CAAA;AAC3B;;;;;;;;"}