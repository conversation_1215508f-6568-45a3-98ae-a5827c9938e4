{"version": 3, "file": "StepperIndicator.cjs", "sources": ["../../src/Stepper/StepperIndicator.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\nimport { injectStepperItemContext } from './StepperItem.vue'\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\n\nexport interface StepperIndicatorProps extends PrimitiveProps { }\n\nconst props = defineProps<StepperIndicatorProps>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current step */\n    step: number\n  }) => any\n}>()\n\nconst itemContext = injectStepperItemContext()\nuseForwardExpose()\n</script>\n\n<template>\n  <Primitive\n    v-bind=\"props\"\n  >\n    <slot :step=\"itemContext.step.value\">\n      Step {{ itemContext.step.value }}\n    </slot>\n  </Primitive>\n</template>\n"], "names": ["injectStepperItemContext", "useForwardExpose"], "mappings": ";;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AASd,IAAA,MAAM,cAAcA,4CAAyB,EAAA;AAC7C,IAAiBC,wCAAA,EAAA;;;;;;;;;;;;;;;;;;"}