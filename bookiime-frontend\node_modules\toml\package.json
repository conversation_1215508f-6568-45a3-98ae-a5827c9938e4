{"name": "toml", "version": "3.0.0", "description": "TOML parser for Node.js (parses TOML spec v0.4.0)", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "pegjs --cache src/toml.pegjs lib/parser.js", "test": "jshint lib/compiler.js && nodeunit test/test_*.js", "prepublish": "npm run build"}, "repository": "git://github.com/BinaryMuse/toml-node.git", "keywords": ["toml", "parser"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"jshint": "*", "nodeunit": "~0.9.0", "pegjs": "~0.8.0"}}