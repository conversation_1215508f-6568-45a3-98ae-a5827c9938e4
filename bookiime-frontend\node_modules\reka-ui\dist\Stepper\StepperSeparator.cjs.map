{"version": 3, "file": "StepperSeparator.cjs", "sources": ["../../src/Stepper/StepperSeparator.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { SeparatorProps } from '@/Separator'\nimport { Separator } from '@/Separator'\nimport { useForwardExpose } from '@/shared'\nimport { injectStepperItemContext } from './StepperItem.vue'\nimport { injectStepperRootContext } from './StepperRoot.vue'\n</script>\n\n<script setup lang=\"ts\">\nexport interface StepperSeparatorProps extends SeparatorProps { }\n\nconst props = withDefaults(defineProps<StepperSeparatorProps>(), {})\n\nconst rootContext = injectStepperRootContext()\nconst itemContext = injectStepperItemContext()\n\nuseForwardExpose()\n</script>\n\n<template>\n  <Separator\n    v-bind=\"props\"\n    decorative\n    :orientation=\"rootContext.orientation.value\"\n    :data-state=\"itemContext.state.value\"\n  >\n    <slot />\n  </Separator>\n</template>\n"], "names": ["injectStepperRootContext", "injectStepperItemContext", "useForwardExpose"], "mappings": ";;;;;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,MAAM,cAAcA,4CAAyB,EAAA;AAC7C,IAAA,MAAM,cAAcC,4CAAyB,EAAA;AAE7C,IAAiBC,wCAAA,EAAA;;;;;;;;;;;;;;;;;;"}