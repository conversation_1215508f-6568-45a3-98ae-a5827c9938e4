{"name": "@oxc-parser/binding-win32-x64-msvc", "version": "0.72.3", "cpu": ["x64"], "main": "parser.win32-x64-msvc.node", "files": ["parser.win32-x64-msvc.node"], "description": "Oxc Parser Node API", "keywords": ["oxc", "parser"], "author": "Boshen and oxc contributors", "homepage": "https://oxc.rs", "license": "MIT", "engines": {"node": ">=14.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/oxc-project/oxc.git", "directory": "napi/parser"}, "bugs": "https://github.com/oxc-project/oxc/issues", "os": ["win32"]}