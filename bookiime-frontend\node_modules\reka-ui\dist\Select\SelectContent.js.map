{"version": 3, "file": "SelectContent.js", "sources": ["../../src/Select/SelectContent.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type {\n  SelectContentImplEmits,\n  SelectContentImplProps,\n} from './SelectContentImpl.vue'\nimport { computed, onMounted, ref, watch } from 'vue'\n\nexport type SelectContentEmits = SelectContentImplEmits\n\nexport interface SelectContentProps extends SelectContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with Vue animation libraries.\n   */\n  forceMount?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { Presence } from '@/Presence'\nimport { useForwardPropsEmits } from '@/shared'\nimport SelectContentImpl from './SelectContentImpl.vue'\nimport SelectProvider from './SelectProvider.vue'\nimport { injectSelectRootContext } from './SelectRoot.vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = defineProps<SelectContentProps>()\n\nconst emits = defineEmits<SelectContentEmits>()\nconst forwarded = useForwardPropsEmits(props, emits)\n\nconst rootContext = injectSelectRootContext()\n\nconst fragment = ref<DocumentFragment>()\nonMounted(() => {\n  fragment.value = new DocumentFragment()\n})\n\nconst presenceRef = ref<InstanceType<typeof Presence>>()\n\nconst present = computed(() => props.forceMount || rootContext.open.value)\nconst renderPresence = ref(present.value)\n\nwatch(present, () => {\n  // Toggle render presence after a delay (nextTick is not enough)\n  // to allow children to re-render with the latest state.\n  // Otherwise, they would remain in the old state during the transition,\n  // which would prevent the animation that depend on state (e.g., data-[state=closed])\n  // from being applied accurately.\n  // @see https://github.com/unovue/reka-ui/issues/1865\n  setTimeout(() => renderPresence.value = present.value)\n})\n</script>\n\n<template>\n  <Presence\n    v-if=\"present || renderPresence || presenceRef?.present\"\n    ref=\"presenceRef\"\n    :present=\"present\"\n  >\n    <SelectContentImpl v-bind=\"{ ...forwarded, ...$attrs }\">\n      <slot />\n    </SelectContentImpl>\n  </Presence>\n\n  <div v-else-if=\"fragment\">\n    <Teleport :to=\"fragment\">\n      <SelectProvider :context=\"rootContext\">\n        <slot />\n      </SelectProvider>\n    </Teleport>\n  </div>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,MAAM,KAAQ,GAAA,MAAA;AACd,IAAM,MAAA,SAAA,GAAY,oBAAqB,CAAA,KAAA,EAAO,KAAK,CAAA;AAEnD,IAAA,MAAM,cAAc,uBAAwB,EAAA;AAE5C,IAAA,MAAM,WAAW,GAAsB,EAAA;AACvC,IAAA,SAAA,CAAU,MAAM;AACd,MAAS,QAAA,CAAA,KAAA,GAAQ,IAAI,gBAAiB,EAAA;AAAA,KACvC,CAAA;AAED,IAAA,MAAM,cAAc,GAAmC,EAAA;AAEvD,IAAA,MAAM,UAAU,QAAS,CAAA,MAAM,MAAM,UAAc,IAAA,WAAA,CAAY,KAAK,KAAK,CAAA;AACzE,IAAM,MAAA,cAAA,GAAiB,GAAI,CAAA,OAAA,CAAQ,KAAK,CAAA;AAExC,IAAA,KAAA,CAAM,SAAS,MAAM;AAOnB,MAAA,UAAA,CAAW,MAAM,cAAA,CAAe,KAAQ,GAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,KACtD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}