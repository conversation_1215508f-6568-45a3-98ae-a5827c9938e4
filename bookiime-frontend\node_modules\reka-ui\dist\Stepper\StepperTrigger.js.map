{"version": 3, "file": "StepperTrigger.js", "sources": ["../../src/Stepper/StepperTrigger.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { computed, onMounted, onUnmounted } from 'vue'\nimport { getActiveElement, useArrowNavigation, useForwardExpose, useKbd } from '@/shared'\n\nexport interface StepperTriggerProps extends PrimitiveProps {\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\nimport { injectStepperItemContext } from './StepperItem.vue'\nimport { injectStepperRootContext } from './StepperRoot.vue'\n\nwithDefaults(defineProps<StepperTriggerProps>(), {\n  as: 'button',\n})\n\nconst rootContext = injectStepperRootContext()\nconst itemContext = injectStepperItemContext()\n\nconst kbd = useKbd()\nconst stepperItems = computed(() => Array.from(rootContext.totalStepperItems.value))\n\nfunction handleMouseDown(event: MouseEvent) {\n  if (itemContext.disabled.value)\n    return\n  if (rootContext.linear.value) {\n    if (itemContext.step.value <= rootContext.modelValue.value! || itemContext.step.value === rootContext.modelValue.value! + 1) {\n      if (event.ctrlKey === false) {\n        rootContext.changeModelValue(itemContext.step.value)\n        return\n      }\n    }\n  }\n  else {\n    if (event.ctrlKey === false) {\n      rootContext.changeModelValue(itemContext.step.value)\n      return\n    }\n  }\n\n  // prevent focus to avoid accidental activation\n  event.preventDefault()\n}\n\nfunction handleKeyDown(event: KeyboardEvent) {\n  event.preventDefault()\n\n  if (itemContext.disabled.value)\n    return\n\n  if ((event.key === kbd.ENTER || event.key === kbd.SPACE) && !event.ctrlKey && !event.shiftKey)\n    rootContext.changeModelValue(itemContext.step.value)\n\n  if ([kbd.ARROW_LEFT, kbd.ARROW_RIGHT, kbd.ARROW_UP, kbd.ARROW_DOWN].includes(event.key)) {\n    useArrowNavigation(event, getActiveElement() as HTMLElement, undefined, {\n      itemsArray: stepperItems.value,\n      focus: true,\n      loop: false,\n      arrowKeyOptions: rootContext.orientation.value,\n      dir: rootContext.dir.value,\n    })\n  }\n}\n\nconst { forwardRef, currentElement } = useForwardExpose()\n\nonMounted(() => {\n  rootContext.totalStepperItems.value.add(currentElement.value)\n})\n\nonUnmounted(() => {\n  rootContext.totalStepperItems.value.delete(currentElement.value)\n})\n</script>\n\n<template>\n  <Primitive\n    :ref=\"forwardRef\"\n    :type=\"as === 'button' ? 'button' : undefined\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    :data-state=\"itemContext.state.value\"\n    :disabled=\"itemContext.disabled.value || !itemContext.isFocusable.value ? '' : undefined\"\n    :data-disabled=\"itemContext.disabled.value || !itemContext.isFocusable.value ? '' : undefined\"\n    :data-orientation=\"rootContext.orientation.value\"\n    :tabindex=\"itemContext.isFocusable.value ? 0 : -1\"\n    :aria-describedby=\"itemContext.descriptionId\"\n    :aria-labelledby=\"itemContext.titleId\"\n    @mousedown.left=\"handleMouseDown\"\n    @keydown.enter.space.left.right.up.down=\"handleKeyDown\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAkBA,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAC7C,IAAA,MAAM,cAAc,wBAAyB,EAAA;AAE7C,IAAA,MAAM,MAAM,MAAO,EAAA;AACnB,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM,KAAA,CAAM,KAAK,WAAY,CAAA,iBAAA,CAAkB,KAAK,CAAC,CAAA;AAEnF,IAAA,SAAS,gBAAgB,KAAmB,EAAA;AAC1C,MAAA,IAAI,YAAY,QAAS,CAAA,KAAA;AACvB,QAAA;AACF,MAAI,IAAA,WAAA,CAAY,OAAO,KAAO,EAAA;AAC5B,QAAA,IAAI,WAAY,CAAA,IAAA,CAAK,KAAS,IAAA,WAAA,CAAY,UAAW,CAAA,KAAA,IAAU,WAAY,CAAA,IAAA,CAAK,KAAU,KAAA,WAAA,CAAY,UAAW,CAAA,KAAA,GAAS,CAAG,EAAA;AAC3H,UAAI,IAAA,KAAA,CAAM,YAAY,KAAO,EAAA;AAC3B,YAAY,WAAA,CAAA,gBAAA,CAAiB,WAAY,CAAA,IAAA,CAAK,KAAK,CAAA;AACnD,YAAA;AAAA;AACF;AACF,OAEG,MAAA;AACH,QAAI,IAAA,KAAA,CAAM,YAAY,KAAO,EAAA;AAC3B,UAAY,WAAA,CAAA,gBAAA,CAAiB,WAAY,CAAA,IAAA,CAAK,KAAK,CAAA;AACnD,UAAA;AAAA;AACF;AAIF,MAAA,KAAA,CAAM,cAAe,EAAA;AAAA;AAGvB,IAAA,SAAS,cAAc,KAAsB,EAAA;AAC3C,MAAA,KAAA,CAAM,cAAe,EAAA;AAErB,MAAA,IAAI,YAAY,QAAS,CAAA,KAAA;AACvB,QAAA;AAEF,MAAA,IAAA,CAAK,KAAM,CAAA,GAAA,KAAQ,GAAI,CAAA,KAAA,IAAS,KAAM,CAAA,GAAA,KAAQ,GAAI,CAAA,KAAA,KAAU,CAAC,KAAA,CAAM,OAAW,IAAA,CAAC,KAAM,CAAA,QAAA;AACnF,QAAY,WAAA,CAAA,gBAAA,CAAiB,WAAY,CAAA,IAAA,CAAK,KAAK,CAAA;AAErD,MAAA,IAAI,CAAC,GAAA,CAAI,UAAY,EAAA,GAAA,CAAI,WAAa,EAAA,GAAA,CAAI,QAAU,EAAA,GAAA,CAAI,UAAU,CAAA,CAAE,QAAS,CAAA,KAAA,CAAM,GAAG,CAAG,EAAA;AACvF,QAAmB,kBAAA,CAAA,KAAA,EAAO,gBAAiB,EAAA,EAAkB,MAAW,EAAA;AAAA,UACtE,YAAY,YAAa,CAAA,KAAA;AAAA,UACzB,KAAO,EAAA,IAAA;AAAA,UACP,IAAM,EAAA,KAAA;AAAA,UACN,eAAA,EAAiB,YAAY,WAAY,CAAA,KAAA;AAAA,UACzC,GAAA,EAAK,YAAY,GAAI,CAAA;AAAA,SACtB,CAAA;AAAA;AACH;AAGF,IAAA,MAAM,EAAE,UAAA,EAAY,cAAe,EAAA,GAAI,gBAAiB,EAAA;AAExD,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,WAAA,CAAY,iBAAkB,CAAA,KAAA,CAAM,GAAI,CAAA,cAAA,CAAe,KAAK,CAAA;AAAA,KAC7D,CAAA;AAED,IAAA,WAAA,CAAY,MAAM;AAChB,MAAA,WAAA,CAAY,iBAAkB,CAAA,KAAA,CAAM,MAAO,CAAA,cAAA,CAAe,KAAK,CAAA;AAAA,KAChE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}