{"version": 3, "file": "registry.cjs", "sources": ["../../src/Splitter/utils/registry.ts"], "sourcesContent": ["import type { Ref } from 'vue'\nimport type { Direction, ResizeEvent } from './types'\nimport { getResizeEventCoordinates } from './events'\nimport { intersects } from './rects'\nimport { compare } from './stackingOrder'\nimport { resetGlobalCursorStyle, setGlobalCursorStyle } from './style'\n\nexport type ResizeHandlerAction = 'down' | 'move' | 'up'\nexport type SetResizeHandlerState = (\n  action: ResizeHandlerAction,\n  isActive: boolean,\n  event: ResizeEvent\n) => void\n\nexport type PointerHitAreaMargins = {\n  coarse: number\n  fine: number\n}\n\nexport type ResizeHandlerData = {\n  direction: Ref<Direction>\n  element: HTMLElement\n  hitAreaMargins: PointerHitAreaMargins\n  setResizeHandlerState: SetResizeHandlerState\n}\n\nexport const EXCEEDED_HORIZONTAL_MIN = 0b0001\nexport const EXCEEDED_HORIZONTAL_MAX = 0b0010\nexport const EXCEEDED_VERTICAL_MIN = 0b0100\nexport const EXCEEDED_VERTICAL_MAX = 0b1000\n\nfunction getInputType(): 'coarse' | 'fine' | undefined {\n  if (typeof matchMedia === 'function')\n    return matchMedia('(pointer:coarse)').matches ? 'coarse' : 'fine'\n}\n\nconst isCoarsePointer = getInputType() === 'coarse'\n\nconst intersectingHandles: ResizeHandlerData[] = []\nlet isPointerDown = false\nconst ownerDocumentCounts: Map<Document, number> = new Map()\nconst panelConstraintFlags: Map<string, number> = new Map()\n\nconst registeredResizeHandlers = new Set<ResizeHandlerData>()\n\nexport function registerResizeHandle(\n  resizeHandleId: string,\n  element: HTMLElement,\n  direction: Ref<Direction>,\n  hitAreaMargins: PointerHitAreaMargins,\n  setResizeHandlerState: SetResizeHandlerState,\n) {\n  const { ownerDocument } = element\n\n  const data: ResizeHandlerData = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState,\n  }\n\n  const count = ownerDocumentCounts.get(ownerDocument) ?? 0\n  ownerDocumentCounts.set(ownerDocument, count + 1)\n\n  registeredResizeHandlers.add(data)\n\n  updateListeners()\n\n  return function unregisterResizeHandle() {\n    panelConstraintFlags.delete(resizeHandleId)\n    registeredResizeHandlers.delete(data)\n\n    const count = ownerDocumentCounts.get(ownerDocument) ?? 1\n    ownerDocumentCounts.set(ownerDocument, count - 1)\n\n    updateListeners()\n    resetGlobalCursorStyle()\n\n    if (count === 1)\n      ownerDocumentCounts.delete(ownerDocument)\n  }\n}\n\nfunction handlePointerDown(event: ResizeEvent) {\n  const { target } = event\n  const { x, y } = getResizeEventCoordinates(event)\n\n  isPointerDown = true\n\n  recalculateIntersectingHandles({ target, x, y })\n  updateListeners()\n\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates('down', event)\n\n    event.preventDefault()\n  }\n}\n\nfunction handlePointerMove(event: ResizeEvent) {\n  const { x, y } = getResizeEventCoordinates(event)\n\n  if (!isPointerDown) {\n    const { target } = event\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({ target, x, y })\n  }\n\n  updateResizeHandlerStates('move', event)\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor()\n\n  if (intersectingHandles.length > 0)\n    event.preventDefault()\n}\n\nfunction handlePointerUp(event: ResizeEvent) {\n  const { target } = event\n  const { x, y } = getResizeEventCoordinates(event)\n\n  panelConstraintFlags.clear()\n  isPointerDown = false\n\n  if (intersectingHandles.length > 0)\n    event.preventDefault()\n\n  updateResizeHandlerStates('up', event)\n  recalculateIntersectingHandles({ target, x, y })\n  updateCursor()\n\n  updateListeners()\n}\n\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y,\n}: {\n  target: EventTarget | null\n  x: number\n  y: number\n}) {\n  intersectingHandles.splice(0)\n\n  let targetElement: HTMLElement | null = null\n  if (target instanceof HTMLElement)\n    targetElement = target\n\n  registeredResizeHandlers.forEach((data) => {\n    const { element: dragHandleElement, hitAreaMargins } = data\n\n    const dragHandleRect = dragHandleElement.getBoundingClientRect()\n    const { bottom, left, right, top } = dragHandleRect\n\n    const margin = isCoarsePointer\n      ? hitAreaMargins.coarse\n      : hitAreaMargins.fine\n\n    const eventIntersects\n      = x >= left - margin\n        && x <= right + margin\n        && y >= top - margin\n        && y <= bottom + margin\n\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (\n        targetElement !== null\n        && dragHandleElement !== targetElement\n        && !dragHandleElement.contains(targetElement)\n        && !targetElement.contains(dragHandleElement)\n        // Calculating stacking order has a cost, so we should avoid it if possible\n        // That is why we only check potentially intersecting handles,\n        // and why we skip if the event target is within the handle's DOM\n        && compare(targetElement, dragHandleElement) > 0\n      ) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement: HTMLElement | null = targetElement\n        let didIntersect = false\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break\n          }\n          else if (\n            intersects(\n              currentElement.getBoundingClientRect(),\n              dragHandleRect,\n              true,\n            )\n          ) {\n            didIntersect = true\n            break\n          }\n\n          currentElement = currentElement.parentElement\n        }\n\n        if (didIntersect)\n          return\n      }\n\n      intersectingHandles.push(data)\n    }\n  })\n}\n\nexport function reportConstraintsViolation(\n  resizeHandleId: string,\n  flag: number,\n) {\n  panelConstraintFlags.set(resizeHandleId, flag)\n}\n\nfunction updateCursor() {\n  let intersectsHorizontal = false\n  let intersectsVertical = false\n\n  intersectingHandles.forEach((data) => {\n    const { direction } = data\n\n    if (direction.value === 'horizontal')\n      intersectsHorizontal = true\n    else\n      intersectsVertical = true\n  })\n\n  let constraintFlags = 0\n  panelConstraintFlags.forEach((flag) => {\n    constraintFlags |= flag\n  })\n\n  if (intersectsHorizontal && intersectsVertical)\n    setGlobalCursorStyle('intersection', constraintFlags)\n  else if (intersectsHorizontal)\n    setGlobalCursorStyle('horizontal', constraintFlags)\n  else if (intersectsVertical)\n    setGlobalCursorStyle('vertical', constraintFlags)\n  else\n    resetGlobalCursorStyle()\n}\n\nfunction updateListeners() {\n  ownerDocumentCounts.forEach((_, ownerDocument) => {\n    const { body } = ownerDocument\n\n    body.removeEventListener('contextmenu', handlePointerUp)\n    body.removeEventListener('mousedown', handlePointerDown)\n    body.removeEventListener('mouseleave', handlePointerMove)\n    body.removeEventListener('mousemove', handlePointerMove)\n    body.removeEventListener('touchmove', handlePointerMove)\n    body.removeEventListener('touchstart', handlePointerDown)\n  })\n\n  window.removeEventListener('mouseup', handlePointerUp)\n  window.removeEventListener('touchcancel', handlePointerUp)\n  window.removeEventListener('touchend', handlePointerUp)\n\n  if (registeredResizeHandlers.size > 0) {\n    if (isPointerDown) {\n      if (intersectingHandles.length > 0) {\n        ownerDocumentCounts.forEach((count, ownerDocument) => {\n          const { body } = ownerDocument\n\n          if (count > 0) {\n            body.addEventListener('contextmenu', handlePointerUp)\n            body.addEventListener('mouseleave', handlePointerMove)\n            body.addEventListener('mousemove', handlePointerMove)\n            body.addEventListener('touchmove', handlePointerMove, {\n              passive: false,\n            })\n          }\n        })\n      }\n\n      window.addEventListener('mouseup', handlePointerUp)\n      window.addEventListener('touchcancel', handlePointerUp)\n      window.addEventListener('touchend', handlePointerUp)\n    }\n    else {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const { body } = ownerDocument\n\n        if (count > 0) {\n          body.addEventListener('mousedown', handlePointerDown)\n          body.addEventListener('mousemove', handlePointerMove)\n          body.addEventListener('touchmove', handlePointerMove, {\n            passive: false,\n          })\n          body.addEventListener('touchstart', handlePointerDown)\n        }\n      })\n    }\n  }\n}\n\nfunction updateResizeHandlerStates(\n  action: ResizeHandlerAction,\n  event: ResizeEvent,\n) {\n  registeredResizeHandlers.forEach((data) => {\n    const { setResizeHandlerState } = data\n\n    const isActive = intersectingHandles.includes(data)\n\n    setResizeHandlerState(action, isActive, event)\n  })\n}\n"], "names": ["count", "resetGlobalCursorStyle", "getResizeEventCoordinates", "compare", "intersects", "setGlobalCursorStyle"], "mappings": ";;;;;;;AA0BO,MAAM,uBAA0B,GAAA;AAChC,MAAM,uBAA0B,GAAA;AAChC,MAAM,qBAAwB,GAAA;AAC9B,MAAM,qBAAwB,GAAA;AAErC,SAAS,YAA8C,GAAA;AACrD,EAAA,IAAI,OAAO,UAAe,KAAA,UAAA;AACxB,IAAA,OAAO,UAAW,CAAA,kBAAkB,CAAE,CAAA,OAAA,GAAU,QAAW,GAAA,MAAA;AAC/D;AAEA,MAAM,eAAA,GAAkB,cAAmB,KAAA,QAAA;AAE3C,MAAM,sBAA2C,EAAC;AAClD,IAAI,aAAgB,GAAA,KAAA;AACpB,MAAM,mBAAA,uBAAiD,GAAI,EAAA;AAC3D,MAAM,oBAAA,uBAAgD,GAAI,EAAA;AAE1D,MAAM,wBAAA,uBAA+B,GAAuB,EAAA;AAErD,SAAS,oBACd,CAAA,cAAA,EACA,OACA,EAAA,SAAA,EACA,gBACA,qBACA,EAAA;AACA,EAAM,MAAA,EAAE,eAAkB,GAAA,OAAA;AAE1B,EAAA,MAAM,IAA0B,GAAA;AAAA,IAC9B,SAAA;AAAA,IACA,OAAA;AAAA,IACA,cAAA;AAAA,IACA;AAAA,GACF;AAEA,EAAA,MAAM,KAAQ,GAAA,mBAAA,CAAoB,GAAI,CAAA,aAAa,CAAK,IAAA,CAAA;AACxD,EAAoB,mBAAA,CAAA,GAAA,CAAI,aAAe,EAAA,KAAA,GAAQ,CAAC,CAAA;AAEhD,EAAA,wBAAA,CAAyB,IAAI,IAAI,CAAA;AAEjC,EAAgB,eAAA,EAAA;AAEhB,EAAA,OAAO,SAAS,sBAAyB,GAAA;AACvC,IAAA,oBAAA,CAAqB,OAAO,cAAc,CAAA;AAC1C,IAAA,wBAAA,CAAyB,OAAO,IAAI,CAAA;AAEpC,IAAA,MAAMA,MAAQ,GAAA,mBAAA,CAAoB,GAAI,CAAA,aAAa,CAAK,IAAA,CAAA;AACxD,IAAoB,mBAAA,CAAA,GAAA,CAAI,aAAeA,EAAAA,MAAAA,GAAQ,CAAC,CAAA;AAEhD,IAAgB,eAAA,EAAA;AAChB,IAAuBC,kCAAA,EAAA;AAEvB,IAAA,IAAID,MAAU,KAAA,CAAA;AACZ,MAAA,mBAAA,CAAoB,OAAO,aAAa,CAAA;AAAA,GAC5C;AACF;AAEA,SAAS,kBAAkB,KAAoB,EAAA;AAC7C,EAAM,MAAA,EAAE,QAAW,GAAA,KAAA;AACnB,EAAA,MAAM,EAAE,CAAA,EAAG,CAAE,EAAA,GAAIE,uCAA0B,KAAK,CAAA;AAEhD,EAAgB,aAAA,GAAA,IAAA;AAEhB,EAAA,8BAAA,CAA+B,EAAE,MAAA,EAAQ,CAAG,EAAA,CAAA,EAAG,CAAA;AAC/C,EAAgB,eAAA,EAAA;AAEhB,EAAI,IAAA,mBAAA,CAAoB,SAAS,CAAG,EAAA;AAClC,IAAA,yBAAA,CAA0B,QAAQ,KAAK,CAAA;AAEvC,IAAA,KAAA,CAAM,cAAe,EAAA;AAAA;AAEzB;AAEA,SAAS,kBAAkB,KAAoB,EAAA;AAC7C,EAAA,MAAM,EAAE,CAAA,EAAG,CAAE,EAAA,GAAIA,uCAA0B,KAAK,CAAA;AAEhD,EAAA,IAAI,CAAC,aAAe,EAAA;AAClB,IAAM,MAAA,EAAE,QAAW,GAAA,KAAA;AAKnB,IAAA,8BAAA,CAA+B,EAAE,MAAA,EAAQ,CAAG,EAAA,CAAA,EAAG,CAAA;AAAA;AAGjD,EAAA,yBAAA,CAA0B,QAAQ,KAAK,CAAA;AAGvC,EAAa,YAAA,EAAA;AAEb,EAAA,IAAI,oBAAoB,MAAS,GAAA,CAAA;AAC/B,IAAA,KAAA,CAAM,cAAe,EAAA;AACzB;AAEA,SAAS,gBAAgB,KAAoB,EAAA;AAC3C,EAAM,MAAA,EAAE,QAAW,GAAA,KAAA;AACnB,EAAA,MAAM,EAAE,CAAA,EAAG,CAAE,EAAA,GAAIA,uCAA0B,KAAK,CAAA;AAEhD,EAAA,oBAAA,CAAqB,KAAM,EAAA;AAC3B,EAAgB,aAAA,GAAA,KAAA;AAEhB,EAAA,IAAI,oBAAoB,MAAS,GAAA,CAAA;AAC/B,IAAA,KAAA,CAAM,cAAe,EAAA;AAEvB,EAAA,yBAAA,CAA0B,MAAM,KAAK,CAAA;AACrC,EAAA,8BAAA,CAA+B,EAAE,MAAA,EAAQ,CAAG,EAAA,CAAA,EAAG,CAAA;AAC/C,EAAa,YAAA,EAAA;AAEb,EAAgB,eAAA,EAAA;AAClB;AAEA,SAAS,8BAA+B,CAAA;AAAA,EACtC,MAAA;AAAA,EACA,CAAA;AAAA,EACA;AACF,CAIG,EAAA;AACD,EAAA,mBAAA,CAAoB,OAAO,CAAC,CAAA;AAE5B,EAAA,IAAI,aAAoC,GAAA,IAAA;AACxC,EAAA,IAAI,MAAkB,YAAA,WAAA;AACpB,IAAgB,aAAA,GAAA,MAAA;AAElB,EAAyB,wBAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACzC,IAAA,MAAM,EAAE,OAAA,EAAS,iBAAmB,EAAA,cAAA,EAAmB,GAAA,IAAA;AAEvD,IAAM,MAAA,cAAA,GAAiB,kBAAkB,qBAAsB,EAAA;AAC/D,IAAA,MAAM,EAAE,MAAA,EAAQ,IAAM,EAAA,KAAA,EAAO,KAAQ,GAAA,cAAA;AAErC,IAAA,MAAM,MAAS,GAAA,eAAA,GACX,cAAe,CAAA,MAAA,GACf,cAAe,CAAA,IAAA;AAEnB,IAAM,MAAA,eAAA,GACF,CAAK,IAAA,IAAA,GAAO,MACT,IAAA,CAAA,IAAK,KAAQ,GAAA,MAAA,IACb,CAAK,IAAA,GAAA,GAAM,MACX,IAAA,CAAA,IAAK,MAAS,GAAA,MAAA;AAErB,IAAA,IAAI,eAAiB,EAAA;AAOnB,MAAA,IACE,kBAAkB,IACf,IAAA,iBAAA,KAAsB,iBACtB,CAAC,iBAAA,CAAkB,SAAS,aAAa,CAAA,IACzC,CAAC,aAAA,CAAc,SAAS,iBAAiB,CAAA,IAIzCC,4BAAQ,aAAe,EAAA,iBAAiB,IAAI,CAC/C,EAAA;AAOA,QAAA,IAAI,cAAqC,GAAA,aAAA;AACzC,QAAA,IAAI,YAAe,GAAA,KAAA;AACnB,QAAA,OAAO,cAAgB,EAAA;AACrB,UAAI,IAAA,cAAA,CAAe,QAAS,CAAA,iBAAiB,CAAG,EAAA;AAC9C,YAAA;AAAA,WAGA,MAAA,IAAAC,sBAAA;AAAA,YACE,eAAe,qBAAsB,EAAA;AAAA,YACrC,cAEF,CACA,EAAA;AACA,YAAe,YAAA,GAAA,IAAA;AACf,YAAA;AAAA;AAGF,UAAA,cAAA,GAAiB,cAAe,CAAA,aAAA;AAAA;AAGlC,QAAI,IAAA,YAAA;AACF,UAAA;AAAA;AAGJ,MAAA,mBAAA,CAAoB,KAAK,IAAI,CAAA;AAAA;AAC/B,GACD,CAAA;AACH;AAEgB,SAAA,0BAAA,CACd,gBACA,IACA,EAAA;AACA,EAAqB,oBAAA,CAAA,GAAA,CAAI,gBAAgB,IAAI,CAAA;AAC/C;AAEA,SAAS,YAAe,GAAA;AACtB,EAAA,IAAI,oBAAuB,GAAA,KAAA;AAC3B,EAAA,IAAI,kBAAqB,GAAA,KAAA;AAEzB,EAAoB,mBAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACpC,IAAM,MAAA,EAAE,WAAc,GAAA,IAAA;AAEtB,IAAA,IAAI,UAAU,KAAU,KAAA,YAAA;AACtB,MAAuB,oBAAA,GAAA,IAAA;AAAA;AAEvB,MAAqB,kBAAA,GAAA,IAAA;AAAA,GACxB,CAAA;AAED,EAAA,IAAI,eAAkB,GAAA,CAAA;AACtB,EAAqB,oBAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACrC,IAAmB,eAAA,IAAA,IAAA;AAAA,GACpB,CAAA;AAED,EAAA,IAAI,oBAAwB,IAAA,kBAAA;AAC1B,IAAAC,gCAAA,CAAqB,gBAAgB,eAAe,CAAA;AAAA,OAC7C,IAAA,oBAAA;AACP,IAAAA,gCAAA,CAAqB,cAAc,eAAe,CAAA;AAAA,OAC3C,IAAA,kBAAA;AACP,IAAAA,gCAAA,CAAqB,YAAY,eAAe,CAAA;AAAA;AAEhD,IAAuBJ,kCAAA,EAAA;AAC3B;AAEA,SAAS,eAAkB,GAAA;AACzB,EAAoB,mBAAA,CAAA,OAAA,CAAQ,CAAC,CAAA,EAAG,aAAkB,KAAA;AAChD,IAAM,MAAA,EAAE,MAAS,GAAA,aAAA;AAEjB,IAAK,IAAA,CAAA,mBAAA,CAAoB,eAAe,eAAe,CAAA;AACvD,IAAK,IAAA,CAAA,mBAAA,CAAoB,aAAa,iBAAiB,CAAA;AACvD,IAAK,IAAA,CAAA,mBAAA,CAAoB,cAAc,iBAAiB,CAAA;AACxD,IAAK,IAAA,CAAA,mBAAA,CAAoB,aAAa,iBAAiB,CAAA;AACvD,IAAK,IAAA,CAAA,mBAAA,CAAoB,aAAa,iBAAiB,CAAA;AACvD,IAAK,IAAA,CAAA,mBAAA,CAAoB,cAAc,iBAAiB,CAAA;AAAA,GACzD,CAAA;AAED,EAAO,MAAA,CAAA,mBAAA,CAAoB,WAAW,eAAe,CAAA;AACrD,EAAO,MAAA,CAAA,mBAAA,CAAoB,eAAe,eAAe,CAAA;AACzD,EAAO,MAAA,CAAA,mBAAA,CAAoB,YAAY,eAAe,CAAA;AAEtD,EAAI,IAAA,wBAAA,CAAyB,OAAO,CAAG,EAAA;AACrC,IAAA,IAAI,aAAe,EAAA;AACjB,MAAI,IAAA,mBAAA,CAAoB,SAAS,CAAG,EAAA;AAClC,QAAoB,mBAAA,CAAA,OAAA,CAAQ,CAAC,KAAA,EAAO,aAAkB,KAAA;AACpD,UAAM,MAAA,EAAE,MAAS,GAAA,aAAA;AAEjB,UAAA,IAAI,QAAQ,CAAG,EAAA;AACb,YAAK,IAAA,CAAA,gBAAA,CAAiB,eAAe,eAAe,CAAA;AACpD,YAAK,IAAA,CAAA,gBAAA,CAAiB,cAAc,iBAAiB,CAAA;AACrD,YAAK,IAAA,CAAA,gBAAA,CAAiB,aAAa,iBAAiB,CAAA;AACpD,YAAK,IAAA,CAAA,gBAAA,CAAiB,aAAa,iBAAmB,EAAA;AAAA,cACpD,OAAS,EAAA;AAAA,aACV,CAAA;AAAA;AACH,SACD,CAAA;AAAA;AAGH,MAAO,MAAA,CAAA,gBAAA,CAAiB,WAAW,eAAe,CAAA;AAClD,MAAO,MAAA,CAAA,gBAAA,CAAiB,eAAe,eAAe,CAAA;AACtD,MAAO,MAAA,CAAA,gBAAA,CAAiB,YAAY,eAAe,CAAA;AAAA,KAEhD,MAAA;AACH,MAAoB,mBAAA,CAAA,OAAA,CAAQ,CAAC,KAAA,EAAO,aAAkB,KAAA;AACpD,QAAM,MAAA,EAAE,MAAS,GAAA,aAAA;AAEjB,QAAA,IAAI,QAAQ,CAAG,EAAA;AACb,UAAK,IAAA,CAAA,gBAAA,CAAiB,aAAa,iBAAiB,CAAA;AACpD,UAAK,IAAA,CAAA,gBAAA,CAAiB,aAAa,iBAAiB,CAAA;AACpD,UAAK,IAAA,CAAA,gBAAA,CAAiB,aAAa,iBAAmB,EAAA;AAAA,YACpD,OAAS,EAAA;AAAA,WACV,CAAA;AACD,UAAK,IAAA,CAAA,gBAAA,CAAiB,cAAc,iBAAiB,CAAA;AAAA;AACvD,OACD,CAAA;AAAA;AACH;AAEJ;AAEA,SAAS,yBAAA,CACP,QACA,KACA,EAAA;AACA,EAAyB,wBAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACzC,IAAM,MAAA,EAAE,uBAA0B,GAAA,IAAA;AAElC,IAAM,MAAA,QAAA,GAAW,mBAAoB,CAAA,QAAA,CAAS,IAAI,CAAA;AAElD,IAAsB,qBAAA,CAAA,MAAA,EAAQ,UAAU,KAAK,CAAA;AAAA,GAC9C,CAAA;AACH;;;;;;;;;"}