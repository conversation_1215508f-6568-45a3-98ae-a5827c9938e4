{"name": "shadcn-nuxt", "type": "module", "version": "2.2.0", "description": "Add shadcn-vue module to Nuxt", "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/unovue/shadcn-vue.git", "directory": "packages/module"}, "exports": {".": {"types": "./dist/types.d.mts", "import": "./dist/module.mjs"}}, "main": "./dist/module.mjs", "files": ["dist"], "dependencies": {"@nuxt/kit": "^3.17.3", "oxc-parser": "^0.72.0"}, "devDependencies": {"@nuxt/eslint-config": "^1.3.1", "@nuxt/module-builder": "^1.0.1", "@nuxt/schema": "^3.17.3", "@nuxt/test-utils": "^3.15.4", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.13.1", "@types/node": "^22.13.1", "nuxt": "^3.17.3", "typescript": "^5.8.3"}, "scripts": {"dev": "nuxi dev playground", "dev:build": "nuxi build playground", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxi prepare playground", "lint": "eslint .", "test": "vitest run", "test:watch": "vitest watch", "release": "pnpm run prepack && pnpm publish && git push --follow-tags", "pub:next": "pnpm prepack && pnpm publish  --no-git-checks --access public --tag next", "pub:release": "pnpm prepack && pnpm publish  --no-git-checks --access public"}}