{"version": 3, "file": "SelectArrow.cjs", "sources": ["../../src/Select/SelectArrow.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PopperArrowProps } from '@/Popper'\n\nexport interface SelectArrowProps extends PopperArrowProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { PopperArrow } from '@/Popper'\nimport { injectSelectContentContext, SelectContentDefaultContextValue } from './SelectContentImpl.vue'\nimport { injectSelectRootContext } from './SelectRoot.vue'\n\nconst props = withDefaults(defineProps<SelectArrowProps>(), {\n  width: 10,\n  height: 5,\n  as: 'svg',\n})\nconst rootContext = injectSelectRootContext()\nconst contentContext = injectSelectContentContext(SelectContentDefaultContextValue)\n</script>\n\n<template>\n  <PopperArrow\n    v-if=\"rootContext.open.value && contentContext.position === 'popper'\"\n    v-bind=\"props\"\n  >\n    <slot />\n  </PopperArrow>\n</template>\n"], "names": ["injectSelectRootContext", "injectSelectContentContext", "SelectContentDefaultContextValue"], "mappings": ";;;;;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,cAAcA,yCAAwB,EAAA;AAC5C,IAAM,MAAA,cAAA,GAAiBC,oDAA2BC,yDAAgC,CAAA;;;;;;;;;;;;;;"}