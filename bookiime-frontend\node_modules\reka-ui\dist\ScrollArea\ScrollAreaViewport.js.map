{"version": 3, "file": "ScrollAreaViewport.js", "sources": ["../../src/ScrollArea/ScrollAreaViewport.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\nimport { useNonce } from '@/shared/useNonce'\n\nexport interface ScrollAreaViewportProps extends PrimitiveProps {\n  /**\n   * Will add `nonce` attribute to the style tag which can be used by Content Security Policy. <br> If omitted, inherits globally from `ConfigProvider`.\n   */\n  nonce?: string\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { onMounted, ref, toRefs } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { injectScrollAreaRootContext } from './ScrollAreaRoot.vue'\n\ndefineOptions({\n  inheritAttrs: false,\n})\n\nconst props = defineProps<ScrollAreaViewportProps>()\n\nconst { nonce: propNonce } = toRefs(props)\nconst nonce = useNonce(propNonce)\n\nconst rootContext = injectScrollAreaRootContext()\n\nconst viewportElement = ref<HTMLElement>()\n\nonMounted(() => {\n  rootContext.onViewportChange(viewportElement.value!)\n  rootContext.onContentChange(contentElement.value!)\n})\n\ndefineExpose({\n  viewportElement,\n})\nconst { forwardRef, currentElement: contentElement } = useForwardExpose()\n</script>\n\n<template>\n  <div\n    ref=\"viewportElement\"\n    data-reka-scroll-area-viewport=\"\"\n    :style=\"{\n      /**\n       * We don't support `visible` because the intention is to have at least one scrollbar\n       * if this component is used and `visible` will behave like `auto` in that case\n       * https://developer.mozilla.org/en-US/docs/Web/CSS/overflowed#description\n       *\n       * We don't handle `auto` because the intention is for the native implementation\n       * to be hidden if using this component. We just want to ensure the node is scrollable\n       * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n       * the browser from having to work out whether to render native scrollbars or not,\n       * we tell it to with the intention of hiding them in CSS.\n       */\n      overflowX: rootContext.scrollbarXEnabled.value ? 'scroll' : 'hidden',\n      overflowY: rootContext.scrollbarYEnabled.value ? 'scroll' : 'hidden',\n    }\"\n    v-bind=\"$attrs\"\n    :tabindex=\"0\"\n  >\n    <Primitive\n      :ref=\"forwardRef\"\n      :style=\"{\n        /**\n         * When horizontal scrollbar is visible: this element should be at least\n         * as wide as its children for size calculations to work correctly.\n         *\n         * When horizontal scrollbar is NOT visible: this element's width should\n         * be constrained by the parent container to enable `text-overflow: ellipsis`\n         */\n        minWidth: rootContext.scrollbarXEnabled.value ? 'fit-content' : undefined,\n      }\"\n      :as-child=\"props.asChild\"\n      :as=\"as\"\n    >\n      <slot />\n    </Primitive>\n  </div>\n  <Primitive\n    as=\"style\"\n    :nonce=\"nonce\"\n  >\n    /* Hide scrollbars cross-browser and enable momentum scroll for touch\n    devices */\n    [data-reka-scroll-area-viewport] {\n    scrollbar-width:none;\n    -ms-overflow-style:none;\n    -webkit-overflow-scrolling:touch;\n    }\n\n    [data-reka-scroll-area-viewport]::-webkit-scrollbar {\n    display:none;\n    }\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAsBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,MAAM,EAAE,KAAA,EAAO,SAAU,EAAA,GAAI,OAAO,KAAK,CAAA;AACzC,IAAM,MAAA,KAAA,GAAQ,SAAS,SAAS,CAAA;AAEhC,IAAA,MAAM,cAAc,2BAA4B,EAAA;AAEhD,IAAA,MAAM,kBAAkB,GAAiB,EAAA;AAEzC,IAAA,SAAA,CAAU,MAAM;AACd,MAAY,WAAA,CAAA,gBAAA,CAAiB,gBAAgB,KAAM,CAAA;AACnD,MAAY,WAAA,CAAA,eAAA,CAAgB,eAAe,KAAM,CAAA;AAAA,KAClD,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX;AAAA,KACD,CAAA;AACD,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,cAAA,KAAmB,gBAAiB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}