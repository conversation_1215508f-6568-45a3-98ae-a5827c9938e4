{"version": 3, "file": "ScrollAreaScrollbarX.cjs", "sources": ["../../src/ScrollArea/ScrollAreaScrollbarX.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed, onMounted } from 'vue'\nimport { useForwardExpose } from '@/shared'\nimport { injectScrollAreaRootContext } from './ScrollAreaRoot.vue'\nimport ScrollAreaScrollbarImpl from './ScrollAreaScrollbarImpl.vue'\nimport { injectScrollAreaScrollbarVisibleContext } from './ScrollAreaScrollbarVisible.vue'\nimport { getThumbSize } from './utils'\n\nconst rootContext = injectScrollAreaRootContext()\nconst scrollbarVisibleContext = injectScrollAreaScrollbarVisibleContext()\n\nconst { forwardRef, currentElement: scrollbarElement } = useForwardExpose()\n\nonMounted(() => {\n  if (scrollbarElement.value)\n    rootContext.onScrollbarXChange(scrollbarElement.value)\n})\nconst sizes = computed(() => scrollbarVisibleContext.sizes.value)\n</script>\n\n<template>\n  <ScrollAreaScrollbarImpl\n    :ref=\"forwardRef\"\n    :is-horizontal=\"true\"\n    data-orientation=\"horizontal\"\n    :style=\"{\n      bottom: 0,\n      left: rootContext.dir.value === 'rtl' ? 'var(--reka-scroll-area-corner-width)' : 0,\n      right: rootContext.dir.value === 'ltr' ? 'var(--reka-scroll-area-corner-width)' : 0,\n      ['--reka-scroll-area-thumb-width' as any]: sizes ? `${getThumbSize(sizes)}px` : undefined,\n    }\"\n    @on-drag-scroll=\"scrollbarVisibleContext.onDragScroll($event.x)\"\n  >\n    <slot />\n  </ScrollAreaScrollbarImpl>\n</template>\n"], "names": ["injectScrollAreaRootContext", "injectScrollAreaScrollbarVisibleContext", "useForwardExpose", "onMounted", "computed"], "mappings": ";;;;;;;;;;;;AAQA,IAAA,MAAM,cAAcA,qDAA4B,EAAA;AAChD,IAAA,MAAM,0BAA0BC,6EAAwC,EAAA;AAExE,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,gBAAA,KAAqBC,wCAAiB,EAAA;AAE1E,IAAAC,aAAA,CAAU,MAAM;AACd,MAAA,IAAI,gBAAiB,CAAA,KAAA;AACnB,QAAY,WAAA,CAAA,kBAAA,CAAmB,iBAAiB,KAAK,CAAA;AAAA,KACxD,CAAA;AACD,IAAA,MAAM,KAAQ,GAAAC,YAAA,CAAS,MAAM,uBAAA,CAAwB,MAAM,KAAK,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}