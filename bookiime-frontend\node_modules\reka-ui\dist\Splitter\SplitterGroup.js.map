{"version": 3, "file": "SplitterGroup.js", "sources": ["../../src/Splitter/SplitterGroup.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { CSSProperties, Ref } from 'vue'\nimport type { PrimitiveProps } from '@/Primitive'\nimport { computed, ref, toRefs, watch, watchEffect } from 'vue'\nimport { areEqual, createContext, useDirection, useForwardExpose, useId } from '@/shared'\nimport { useWindowSplitterPanelGroupBehavior } from './utils/composables/useWindowSplitterPanelGroupBehavior'\nimport {\n  initializeDefaultStorage,\n  loadPanelGroupState,\n  savePanelGroupState,\n} from './utils/storage'\n\nexport interface SplitterGroupProps extends PrimitiveProps {\n  /** Group id; falls back to `useId` when not provided. */\n  id?: string | null\n  /** Unique id used to auto-save group arrangement via `localStorage`. */\n  autoSaveId?: string | null\n  /** The group orientation of splitter. */\n  direction: Direction\n  /** Step size when arrow key was pressed. */\n  keyboardResizeBy?: number | null\n  /** Custom storage API; defaults to localStorage */\n  storage?: PanelGroupStorage\n}\n\nexport type SplitterGroupEmits = {\n  /** Event handler called when group layout changes */\n  layout: [val: number[]]\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100\n\nexport type PanelGroupStorage = {\n  getItem: (name: string) => string | null\n  setItem: (name: string, value: string) => void\n}\n\nconst defaultStorage: PanelGroupStorage = {\n  getItem: (name: string) => {\n    initializeDefaultStorage(defaultStorage)\n    return defaultStorage.getItem(name)\n  },\n  setItem: (name: string, value: string) => {\n    initializeDefaultStorage(defaultStorage)\n    defaultStorage.setItem(name, value)\n  },\n}\n\nexport type PanelGroupContext = {\n  direction: Ref<Direction>\n  dragState: DragState | null\n  groupId: string\n  reevaluatePanelConstraints: (panelData: PanelData, prevConstraints: PanelConstraints) => void\n  registerPanel: (panelData: PanelData) => void\n  registerResizeHandle: (dragHandleId: string) => ResizeHandler\n  resizePanel: (panelData: PanelData, size: number) => void\n  startDragging: (dragHandleId: string, event: ResizeEvent) => void\n  stopDragging: () => void\n  unregisterPanel: (panelData: PanelData) => void\n  panelGroupElement: Ref<ParentNode | null>\n\n  // Exposed function for child component\n  collapsePanel: (panelData: PanelData) => void\n  expandPanel: (panelData: PanelData) => void\n  isPanelCollapsed: (panelData: PanelData) => boolean\n  isPanelExpanded: (panelData: PanelData) => boolean\n  getPanelSize: (panelData: PanelData) => number\n  getPanelStyle: (panelData: PanelData, defaultSize: number | undefined) => CSSProperties\n}\n\nexport const [injectPanelGroupContext, providePanelGroupContext] = createContext<PanelGroupContext>('PanelGroup')\n</script>\n\n<script setup lang=\"ts\">\nimport type { PanelConstraints, PanelData } from './SplitterPanel.vue'\n\nimport type { Direction, DragState, ResizeEvent, ResizeHandler } from './utils/types'\nimport { Primitive } from '@/Primitive'\nimport { assert } from './utils/assert'\nimport { calculateDeltaPercentage, calculateUnsafeDefaultLayout } from './utils/calculate'\nimport { callPanelCallbacks } from './utils/callPanelCallbacks'\nimport debounce from './utils/debounce'\nimport { getResizeHandleElement } from './utils/dom'\nimport { getResizeEventCursorPosition, isKeyDown, isMouseEvent, isTouchEvent } from './utils/events'\nimport { adjustLayoutByDelta, compareLayouts } from './utils/layout'\nimport { determinePivotIndices } from './utils/pivot'\nimport {\n  EXCEEDED_HORIZONTAL_MAX,\n  EXCEEDED_HORIZONTAL_MIN,\n  EXCEEDED_VERTICAL_MAX,\n  EXCEEDED_VERTICAL_MIN,\n  reportConstraintsViolation,\n} from './utils/registry'\nimport { computePanelFlexBoxStyle } from './utils/style'\nimport { validatePanelGroupLayout } from './utils/validation'\n\nconst props = withDefaults(defineProps<SplitterGroupProps>(), {\n  autoSaveId: null,\n  keyboardResizeBy: 10,\n  storage: () => defaultStorage,\n})\nconst emits = defineEmits<SplitterGroupEmits>()\n\ndefineSlots<{\n  default?: (props: {\n    /** Current size of layout */\n    layout: typeof layout.value\n  }) => any\n}>()\n\nconst debounceMap: {\n  [key: string]: typeof savePanelGroupState\n} = {}\n\nconst { direction } = toRefs(props)\nconst groupId = useId(props.id, 'reka-splitter-group')\nconst dir = useDirection()\nconst { forwardRef, currentElement: panelGroupElementRef } = useForwardExpose()\n\nconst dragState = ref<DragState | null>(null)\nconst layout = ref<number[]>([])\nconst panelIdToLastNotifiedSizeMapRef = ref<Record<string, number>>({})\nconst panelSizeBeforeCollapseRef = ref<Map<string, number>>(new Map())\nconst prevDeltaRef = ref<number>(0)\n\nconst committedValuesRef = computed(() => ({\n  autoSaveId: props.autoSaveId,\n  direction: props.direction,\n  dragState: dragState.value,\n  id: groupId,\n  keyboardResizeBy: props.keyboardResizeBy,\n  storage: props.storage,\n}) satisfies {\n  autoSaveId: string | null\n  direction: Direction\n  dragState: DragState | null\n  id: string\n  keyboardResizeBy: number | null\n  storage: PanelGroupStorage\n})\n\nconst eagerValuesRef = ref<{\n  layout: number[]\n  panelDataArray: PanelData[]\n  panelDataArrayChanged: boolean\n}>({\n  layout: layout.value,\n  panelDataArray: [],\n  panelDataArrayChanged: false,\n})\n\nconst setLayout = (val: number[]) => layout.value = val\n\nuseWindowSplitterPanelGroupBehavior({\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray: eagerValuesRef.value.panelDataArray,\n  setLayout,\n  panelGroupElement: panelGroupElementRef,\n})\n\nwatchEffect(() => {\n  const { panelDataArray } = eagerValuesRef.value\n  const { autoSaveId } = props\n  // If this panel has been configured to persist sizing information, save sizes to local storage.\n  if (autoSaveId) {\n    if (layout.value.length === 0 || layout.value.length !== panelDataArray.length)\n      return\n\n    let debouncedSave = debounceMap[autoSaveId]\n\n    // Limit the frequency of localStorage updates.\n    if (!debouncedSave) {\n      debouncedSave = debounce(\n        savePanelGroupState,\n        LOCAL_STORAGE_DEBOUNCE_INTERVAL,\n      )\n\n      debounceMap[autoSaveId] = debouncedSave\n    }\n\n    // Clone mutable data before passing to the debounced function,\n    // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n    const clonedPanelDataArray = [...panelDataArray]\n    const clonedPanelSizesBeforeCollapse = new Map(\n      panelSizeBeforeCollapseRef.value,\n    )\n\n    debouncedSave(\n      autoSaveId,\n      clonedPanelDataArray,\n      clonedPanelSizesBeforeCollapse,\n      layout.value,\n      props.storage,\n    )\n  }\n})\n\nfunction getPanelStyle(panelData: PanelData, defaultSize: number | undefined) {\n  const { panelDataArray } = eagerValuesRef.value\n\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData)\n\n  return computePanelFlexBoxStyle({\n    defaultSize,\n    dragState: dragState.value,\n    layout: layout.value,\n    panelData: panelDataArray,\n    panelIndex,\n  })\n}\n\nfunction registerPanel(panelData: PanelData) {\n  const { panelDataArray } = eagerValuesRef.value\n\n  panelDataArray.push(panelData)\n  panelDataArray.sort((panelA, panelB) => {\n    const orderA = panelA.order\n    const orderB = panelB.order\n    if (orderA == null && orderB == null)\n      return 0\n    else if (orderA == null)\n      return -1\n    else if (orderB == null)\n      return 1\n    else\n      return orderA - orderB\n  })\n\n  eagerValuesRef.value.panelDataArrayChanged = true\n}\n\n// (Re)calculate group layout whenever panels are registered or unregistered.\n// useIsomorphicLayoutEffect\nwatch(() => eagerValuesRef.value.panelDataArrayChanged, () => {\n  if (eagerValuesRef.value.panelDataArrayChanged) {\n    eagerValuesRef.value.panelDataArrayChanged = false\n\n    const { autoSaveId, storage } = committedValuesRef.value\n    const { layout: prevLayout, panelDataArray } = eagerValuesRef.value\n\n    // If this panel has been configured to persist sizing information,\n    // default size should be restored from local storage if possible.\n    let unsafeLayout: number[] | null = null\n    if (autoSaveId) {\n      const state = loadPanelGroupState(autoSaveId, panelDataArray, storage)\n      if (state) {\n        panelSizeBeforeCollapseRef.value = new Map(\n          Object.entries(state.expandToSizes),\n        )\n        unsafeLayout = state.layout\n      }\n    }\n\n    if (unsafeLayout === null) {\n      unsafeLayout = calculateUnsafeDefaultLayout({\n        panelDataArray,\n      })\n    }\n\n    // Validate even saved layouts in case something has changed since last render\n    // e.g. for pixel groups, this could be the size of the window\n    const nextLayout = validatePanelGroupLayout({\n      layout: unsafeLayout,\n      panelConstraints: panelDataArray.map(\n        panelData => panelData.constraints,\n      ),\n    })\n\n    if (!areEqual(prevLayout, nextLayout)) {\n      setLayout(nextLayout)\n\n      eagerValuesRef.value.layout = nextLayout\n      emits('layout', nextLayout)\n\n      callPanelCallbacks(\n        panelDataArray,\n        nextLayout,\n        panelIdToLastNotifiedSizeMapRef.value,\n      )\n    }\n  }\n})\n\nfunction registerResizeHandle(dragHandleId: string) {\n  return function resizeHandler(event: ResizeEvent) {\n    event.preventDefault()\n    const panelGroupElement = panelGroupElementRef.value\n    if (!panelGroupElement)\n      return () => null\n\n    const { direction, dragState, id: groupId, keyboardResizeBy } = committedValuesRef.value\n    const { layout: prevLayout, panelDataArray } = eagerValuesRef.value\n\n    const { initialLayout } = dragState ?? {}\n\n    const pivotIndices = determinePivotIndices(\n      groupId,\n      dragHandleId,\n      panelGroupElement,\n    )\n\n    let delta = calculateDeltaPercentage(\n      event,\n      dragHandleId,\n      direction,\n      dragState,\n      keyboardResizeBy,\n      panelGroupElement,\n    )\n    if (delta === 0)\n      return\n\n    // Support RTL layouts\n    const isHorizontal = direction === 'horizontal'\n    if (dir.value === 'rtl' && isHorizontal)\n      delta = -delta\n\n    const panelConstraints = panelDataArray.map(panelData => panelData.constraints)\n\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      layout: initialLayout ?? prevLayout,\n      panelConstraints,\n      pivotIndices,\n      trigger: isKeyDown(event) ? 'keyboard' : 'mouse-or-touch',\n    })\n\n    const layoutChanged = !compareLayouts(prevLayout, nextLayout)\n\n    // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n    // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n    if (isMouseEvent(event) || isTouchEvent(event)) {\n      // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n      // In this case, Panel sizes might not change–\n      // but updating cursor in this scenario would cause a flicker.\n      if (prevDeltaRef.value !== delta) {\n        prevDeltaRef.value = delta\n\n        if (!layoutChanged) {\n          // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n          // This mimics VS Code behavior.\n          if (isHorizontal) {\n            reportConstraintsViolation(\n              dragHandleId,\n              delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX,\n            )\n          }\n          else {\n            reportConstraintsViolation(\n              dragHandleId,\n              delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX,\n            )\n          }\n        }\n        else {\n          reportConstraintsViolation(dragHandleId, 0)\n        }\n      }\n    }\n\n    if (layoutChanged) {\n      setLayout(nextLayout)\n\n      eagerValuesRef.value.layout = nextLayout\n      emits('layout', nextLayout)\n\n      callPanelCallbacks(\n        panelDataArray,\n        nextLayout,\n        panelIdToLastNotifiedSizeMapRef.value,\n      )\n    }\n  }\n}\n\nfunction resizePanel(panelData: PanelData, unsafePanelSize: number) {\n  const { layout: prevLayout, panelDataArray } = eagerValuesRef.value\n\n  const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints)\n\n  const { panelSize, pivotIndices } = panelDataHelper(\n    panelDataArray,\n    panelData,\n    prevLayout,\n  )\n\n  assert(panelSize != null)\n\n  const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1\n  const delta = isLastPanel\n    ? panelSize - unsafePanelSize\n    : unsafePanelSize - panelSize\n\n  const nextLayout = adjustLayoutByDelta({\n    delta,\n    layout: prevLayout,\n    panelConstraints: panelConstraintsArray,\n    pivotIndices,\n    trigger: 'imperative-api',\n  })\n\n  if (!compareLayouts(prevLayout, nextLayout)) {\n    setLayout(nextLayout)\n\n    eagerValuesRef.value.layout = nextLayout\n    emits('layout', nextLayout)\n\n    callPanelCallbacks(\n      panelDataArray,\n      nextLayout,\n      panelIdToLastNotifiedSizeMapRef.value,\n    )\n  }\n}\n\nfunction reevaluatePanelConstraints(panelData: PanelData, prevConstraints: PanelConstraints) {\n  const { layout, panelDataArray } = eagerValuesRef.value\n  const index = findPanelDataIndex(panelDataArray, panelData)\n  panelDataArray[index] = panelData\n  eagerValuesRef.value.panelDataArrayChanged = true\n  const {\n    collapsedSize: prevCollapsedSize = 0,\n    collapsible: prevCollapsible,\n  } = prevConstraints\n\n  const {\n    collapsedSize: nextCollapsedSize = 0,\n    collapsible: nextCollapsible,\n    maxSize: nextMaxSize = 100,\n    minSize: nextMinSize = 0,\n  } = panelData.constraints\n\n  const { panelSize: prevPanelSize } = panelDataHelper(\n    panelDataArray,\n    panelData,\n    layout,\n  )\n  if (prevPanelSize === null) {\n    // It's possible that the panels in this group have changed since the last render\n    return\n  }\n\n  if (\n    prevCollapsible\n    && nextCollapsible\n    && prevPanelSize === prevCollapsedSize\n  ) {\n    if (prevCollapsedSize !== nextCollapsedSize) {\n      resizePanel(panelData, nextCollapsedSize)\n    }\n    else {\n      // Stay collapsed\n    }\n  }\n  else if (prevPanelSize < nextMinSize) {\n    resizePanel(panelData, nextMinSize)\n  }\n  else if (prevPanelSize > nextMaxSize) {\n    resizePanel(panelData, nextMaxSize)\n  }\n}\n\nfunction startDragging(dragHandleId: string, event: ResizeEvent) {\n  const { direction } = committedValuesRef.value\n  const { layout } = eagerValuesRef.value\n  if (!panelGroupElementRef.value)\n    return\n\n  const handleElement = getResizeHandleElement(\n    dragHandleId,\n    panelGroupElementRef.value,\n  )\n  assert(handleElement)\n\n  const initialCursorPosition = getResizeEventCursorPosition(\n    direction,\n    event,\n  )\n\n  dragState.value = {\n    dragHandleId,\n    dragHandleRect: handleElement.getBoundingClientRect(),\n    initialCursorPosition,\n    initialLayout: layout,\n  }\n}\nfunction stopDragging() {\n  dragState.value = null\n}\n\nfunction unregisterPanel(panelData: PanelData) {\n  const { panelDataArray } = eagerValuesRef.value\n\n  const index = findPanelDataIndex(panelDataArray, panelData)\n  if (index >= 0) {\n    panelDataArray.splice(index, 1)\n\n    // TRICKY\n    // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n    // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n    // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n    delete panelIdToLastNotifiedSizeMapRef.value[panelData.id]\n\n    eagerValuesRef.value.panelDataArrayChanged = true\n  }\n}\n\nfunction collapsePanel(panelData: PanelData) {\n  const { layout: prevLayout, panelDataArray } = eagerValuesRef.value\n\n  if (panelData.constraints.collapsible) {\n    const panelConstraintsArray = panelDataArray.map(\n      panelData => panelData.constraints,\n    )\n\n    const {\n      collapsedSize = 0,\n      panelSize,\n      pivotIndices,\n    } = panelDataHelper(panelDataArray, panelData, prevLayout)\n\n    assert(\n      panelSize != null,\n      `Panel size not found for panel \"${panelData.id}\"`,\n    )\n\n    if (panelSize !== collapsedSize) {\n      // Store size before collapse;\n      // This is the size that gets restored if the expand() API is used.\n      panelSizeBeforeCollapseRef.value.set(panelData.id, panelSize)\n\n      const isLastPanel\n          = findPanelDataIndex(panelDataArray, panelData)\n            === panelDataArray.length - 1\n      const delta = isLastPanel\n        ? panelSize - collapsedSize\n        : collapsedSize - panelSize\n\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        layout: prevLayout,\n        panelConstraints: panelConstraintsArray,\n        pivotIndices,\n        trigger: 'imperative-api',\n      })\n\n      if (!compareLayouts(prevLayout, nextLayout)) {\n        setLayout(nextLayout)\n\n        eagerValuesRef.value.layout = nextLayout\n\n        emits('layout', nextLayout)\n\n        callPanelCallbacks(\n          panelDataArray,\n          nextLayout,\n          panelIdToLastNotifiedSizeMapRef.value,\n        )\n      }\n    }\n  }\n}\n\nfunction expandPanel(panelData: PanelData) {\n  const { layout: prevLayout, panelDataArray } = eagerValuesRef.value\n\n  if (panelData.constraints.collapsible) {\n    const panelConstraintsArray = panelDataArray.map(\n      panelData => panelData.constraints,\n    )\n\n    const {\n      collapsedSize = 0,\n      panelSize,\n      minSize = 0,\n      pivotIndices,\n    } = panelDataHelper(panelDataArray, panelData, prevLayout)\n\n    if (panelSize === collapsedSize) {\n      // Restore this panel to the size it was before it was collapsed, if possible.\n      const prevPanelSize = panelSizeBeforeCollapseRef.value.get(\n        panelData.id,\n      )\n\n      const baseSize\n          = prevPanelSize != null && prevPanelSize >= minSize\n            ? prevPanelSize\n            : minSize\n\n      const isLastPanel\n          = findPanelDataIndex(panelDataArray, panelData)\n            === panelDataArray.length - 1\n      const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize\n\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        layout: prevLayout,\n        panelConstraints: panelConstraintsArray,\n        pivotIndices,\n        trigger: 'imperative-api',\n      })\n\n      if (!compareLayouts(prevLayout, nextLayout)) {\n        setLayout(nextLayout)\n\n        eagerValuesRef.value.layout = nextLayout\n\n        emits('layout', nextLayout)\n\n        callPanelCallbacks(\n          panelDataArray,\n          nextLayout,\n          panelIdToLastNotifiedSizeMapRef.value,\n        )\n      }\n    }\n  }\n}\n\nfunction getPanelSize(panelData: PanelData) {\n  const { layout, panelDataArray } = eagerValuesRef.value\n\n  const { panelSize } = panelDataHelper(panelDataArray, panelData, layout)\n\n  assert(\n    panelSize != null,\n    `Panel size not found for panel \"${panelData.id}\"`,\n  )\n\n  return panelSize\n}\n\nfunction isPanelCollapsed(panelData: PanelData) {\n  const { layout, panelDataArray } = eagerValuesRef.value\n\n  const {\n    collapsedSize = 0,\n    collapsible,\n    panelSize,\n  } = panelDataHelper(panelDataArray, panelData, layout)\n\n  if (!collapsible)\n    return false\n\n  // panelSize is undefined during ssr due to vue ssr reactivity limitation.\n  if (panelSize === undefined) {\n    return panelData.constraints.defaultSize === panelData.constraints.collapsedSize\n  }\n  else {\n    return panelSize === collapsedSize\n  }\n}\n\nfunction isPanelExpanded(panelData: PanelData) {\n  const { layout, panelDataArray } = eagerValuesRef.value\n\n  const {\n    collapsedSize = 0,\n    collapsible,\n    panelSize,\n  } = panelDataHelper(panelDataArray, panelData, layout)\n\n  assert(\n    panelSize != null,\n    `Panel size not found for panel \"${panelData.id}\"`,\n  )\n\n  return !collapsible || panelSize > collapsedSize\n}\n\nprovidePanelGroupContext({\n  direction,\n  dragState: dragState.value,\n  groupId,\n  reevaluatePanelConstraints,\n  registerPanel,\n  registerResizeHandle,\n  resizePanel,\n  startDragging,\n  stopDragging,\n  unregisterPanel,\n  panelGroupElement: panelGroupElementRef,\n\n  collapsePanel,\n  expandPanel,\n  isPanelCollapsed,\n  isPanelExpanded,\n  getPanelSize,\n  getPanelStyle,\n})\n\nfunction findPanelDataIndex(panelDataArray: PanelData[], panelData: PanelData) {\n  return panelDataArray.findIndex(\n    prevPanelData =>\n      prevPanelData === panelData || prevPanelData.id === panelData.id,\n  )\n}\n\nfunction panelDataHelper(\n  panelDataArray: PanelData[],\n  panelData: PanelData,\n  layout: number[],\n) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData)\n\n  const isLastPanel = panelIndex === panelDataArray.length - 1\n  const pivotIndices = isLastPanel\n    ? [panelIndex - 1, panelIndex]\n    : [panelIndex, panelIndex + 1]\n\n  const panelSize = layout[panelIndex]\n\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices,\n  }\n}\n</script>\n\n<template>\n  <Primitive\n    :ref=\"forwardRef\"\n    :as=\"as\"\n    :as-child=\"asChild\"\n    :style=\"{\n      display: 'flex',\n      flexDirection: direction === 'horizontal' ? 'row' : 'column',\n      height: '100%',\n      overflow: 'hidden',\n      width: '100%',\n    }\"\n    data-panel-group=\"\"\n    :data-orientation=\"direction\"\n    :data-panel-group-id=\"groupId\"\n  >\n    <slot :layout=\"layout\" />\n  </Primitive>\n</template>\n"], "names": ["direction", "dragState", "groupId", "panelData", "layout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA8BA,MAAM,+BAAkC,GAAA,GAAA;AAOxC,MAAM,cAAoC,GAAA;AAAA,EACxC,OAAA,EAAS,CAAC,IAAiB,KAAA;AACzB,IAAA,wBAAA,CAAyB,cAAc,CAAA;AACvC,IAAO,OAAA,cAAA,CAAe,QAAQ,IAAI,CAAA;AAAA,GACpC;AAAA,EACA,OAAA,EAAS,CAAC,IAAA,EAAc,KAAkB,KAAA;AACxC,IAAA,wBAAA,CAAyB,cAAc,CAAA;AACvC,IAAe,cAAA,CAAA,OAAA,CAAQ,MAAM,KAAK,CAAA;AAAA;AAEtC,CAAA;AAwBO,MAAM,CAAC,uBAAA,EAAyB,wBAAwB,CAAA,GAAI,cAAiC,YAAY;;;;;;;;;;;;;;AA0BhH,IAAA,MAAM,KAAQ,GAAA,OAAA;AAKd,IAAA,MAAM,KAAQ,GAAA,MAAA;AASd,IAAA,MAAM,cAEF,EAAC;AAEL,IAAA,MAAM,EAAE,SAAA,EAAc,GAAA,MAAA,CAAO,KAAK,CAAA;AAClC,IAAA,MAAM,OAAU,GAAA,KAAA,CAAM,KAAM,CAAA,EAAA,EAAI,qBAAqB,CAAA;AACrD,IAAA,MAAM,MAAM,YAAa,EAAA;AACzB,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,oBAAA,KAAyB,gBAAiB,EAAA;AAE9E,IAAM,MAAA,SAAA,GAAY,IAAsB,IAAI,CAAA;AAC5C,IAAM,MAAA,MAAA,GAAS,GAAc,CAAA,EAAE,CAAA;AAC/B,IAAM,MAAA,+BAAA,GAAkC,GAA4B,CAAA,EAAE,CAAA;AACtE,IAAA,MAAM,0BAA6B,GAAA,GAAA,iBAA6B,IAAA,GAAA,EAAK,CAAA;AACrE,IAAM,MAAA,YAAA,GAAe,IAAY,CAAC,CAAA;AAElC,IAAM,MAAA,kBAAA,GAAqB,SAAS,OAAO;AAAA,MACzC,YAAY,KAAM,CAAA,UAAA;AAAA,MAClB,WAAW,KAAM,CAAA,SAAA;AAAA,MACjB,WAAW,SAAU,CAAA,KAAA;AAAA,MACrB,EAAI,EAAA,OAAA;AAAA,MACJ,kBAAkB,KAAM,CAAA,gBAAA;AAAA,MACxB,SAAS,KAAM,CAAA;AAAA,KAQhB,CAAA,CAAA;AAED,IAAA,MAAM,iBAAiB,GAIpB,CAAA;AAAA,MACD,QAAQ,MAAO,CAAA,KAAA;AAAA,MACf,gBAAgB,EAAC;AAAA,MACjB,qBAAuB,EAAA;AAAA,KACxB,CAAA;AAED,IAAA,MAAM,SAAY,GAAA,CAAC,GAAkB,KAAA,MAAA,CAAO,KAAQ,GAAA,GAAA;AAEpD,IAAoC,mCAAA,CAAA;AAAA,MAClC,cAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,cAAA,EAAgB,eAAe,KAAM,CAAA,cAAA;AAAA,MACrC,SAAA;AAAA,MACA,iBAAmB,EAAA;AAAA,KACpB,CAAA;AAED,IAAA,WAAA,CAAY,MAAM;AAChB,MAAM,MAAA,EAAE,cAAe,EAAA,GAAI,cAAe,CAAA,KAAA;AAC1C,MAAM,MAAA,EAAE,YAAe,GAAA,KAAA;AAEvB,MAAA,IAAI,UAAY,EAAA;AACd,QAAA,IAAI,OAAO,KAAM,CAAA,MAAA,KAAW,KAAK,MAAO,CAAA,KAAA,CAAM,WAAW,cAAe,CAAA,MAAA;AACtE,UAAA;AAEF,QAAI,IAAA,aAAA,GAAgB,YAAY,UAAU,CAAA;AAG1C,QAAA,IAAI,CAAC,aAAe,EAAA;AAClB,UAAgB,aAAA,GAAA,QAAA;AAAA,YACd,mBAAA;AAAA,YACA;AAAA,WACF;AAEA,UAAA,WAAA,CAAY,UAAU,CAAI,GAAA,aAAA;AAAA;AAK5B,QAAM,MAAA,oBAAA,GAAuB,CAAC,GAAG,cAAc,CAAA;AAC/C,QAAA,MAAM,iCAAiC,IAAI,GAAA;AAAA,UACzC,0BAA2B,CAAA;AAAA,SAC7B;AAEA,QAAA,aAAA;AAAA,UACE,UAAA;AAAA,UACA,oBAAA;AAAA,UACA,8BAAA;AAAA,UACA,MAAO,CAAA,KAAA;AAAA,UACP,KAAM,CAAA;AAAA,SACR;AAAA;AACF,KACD,CAAA;AAED,IAAS,SAAA,aAAA,CAAc,WAAsB,WAAiC,EAAA;AAC5E,MAAM,MAAA,EAAE,cAAe,EAAA,GAAI,cAAe,CAAA,KAAA;AAE1C,MAAM,MAAA,UAAA,GAAa,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA;AAE/D,MAAA,OAAO,wBAAyB,CAAA;AAAA,QAC9B,WAAA;AAAA,QACA,WAAW,SAAU,CAAA,KAAA;AAAA,QACrB,QAAQ,MAAO,CAAA,KAAA;AAAA,QACf,SAAW,EAAA,cAAA;AAAA,QACX;AAAA,OACD,CAAA;AAAA;AAGH,IAAA,SAAS,cAAc,SAAsB,EAAA;AAC3C,MAAM,MAAA,EAAE,cAAe,EAAA,GAAI,cAAe,CAAA,KAAA;AAE1C,MAAA,cAAA,CAAe,KAAK,SAAS,CAAA;AAC7B,MAAe,cAAA,CAAA,IAAA,CAAK,CAAC,MAAA,EAAQ,MAAW,KAAA;AACtC,QAAA,MAAM,SAAS,MAAO,CAAA,KAAA;AACtB,QAAA,MAAM,SAAS,MAAO,CAAA,KAAA;AACtB,QAAI,IAAA,MAAA,IAAU,QAAQ,MAAU,IAAA,IAAA;AAC9B,UAAO,OAAA,CAAA;AAAA,aAAA,IACA,MAAU,IAAA,IAAA;AACjB,UAAO,OAAA,EAAA;AAAA,aAAA,IACA,MAAU,IAAA,IAAA;AACjB,UAAO,OAAA,CAAA;AAAA;AAEP,UAAA,OAAO,MAAS,GAAA,MAAA;AAAA,OACnB,CAAA;AAED,MAAA,cAAA,CAAe,MAAM,qBAAwB,GAAA,IAAA;AAAA;AAK/C,IAAA,KAAA,CAAM,MAAM,cAAA,CAAe,KAAM,CAAA,qBAAA,EAAuB,MAAM;AAC5D,MAAI,IAAA,cAAA,CAAe,MAAM,qBAAuB,EAAA;AAC9C,QAAA,cAAA,CAAe,MAAM,qBAAwB,GAAA,KAAA;AAE7C,QAAA,MAAM,EAAE,UAAA,EAAY,OAAQ,EAAA,GAAI,kBAAmB,CAAA,KAAA;AACnD,QAAA,MAAM,EAAE,MAAA,EAAQ,UAAY,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAI9D,QAAA,IAAI,YAAgC,GAAA,IAAA;AACpC,QAAA,IAAI,UAAY,EAAA;AACd,UAAA,MAAM,KAAQ,GAAA,mBAAA,CAAoB,UAAY,EAAA,cAAA,EAAgB,OAAO,CAAA;AACrE,UAAA,IAAI,KAAO,EAAA;AACT,YAAA,0BAAA,CAA2B,QAAQ,IAAI,GAAA;AAAA,cACrC,MAAA,CAAO,OAAQ,CAAA,KAAA,CAAM,aAAa;AAAA,aACpC;AACA,YAAA,YAAA,GAAe,KAAM,CAAA,MAAA;AAAA;AACvB;AAGF,QAAA,IAAI,iBAAiB,IAAM,EAAA;AACzB,UAAA,YAAA,GAAe,4BAA6B,CAAA;AAAA,YAC1C;AAAA,WACD,CAAA;AAAA;AAKH,QAAA,MAAM,aAAa,wBAAyB,CAAA;AAAA,UAC1C,MAAQ,EAAA,YAAA;AAAA,UACR,kBAAkB,cAAe,CAAA,GAAA;AAAA,YAC/B,eAAa,SAAU,CAAA;AAAA;AACzB,SACD,CAAA;AAED,QAAA,IAAI,CAAC,QAAA,CAAS,UAAY,EAAA,UAAU,CAAG,EAAA;AACrC,UAAA,SAAA,CAAU,UAAU,CAAA;AAEpB,UAAA,cAAA,CAAe,MAAM,MAAS,GAAA,UAAA;AAC9B,UAAA,KAAA,CAAM,UAAU,UAAU,CAAA;AAE1B,UAAA,kBAAA;AAAA,YACE,cAAA;AAAA,YACA,UAAA;AAAA,YACA,+BAAgC,CAAA;AAAA,WAClC;AAAA;AACF;AACF,KACD,CAAA;AAED,IAAA,SAAS,qBAAqB,YAAsB,EAAA;AAClD,MAAO,OAAA,SAAS,cAAc,KAAoB,EAAA;AAChD,QAAA,KAAA,CAAM,cAAe,EAAA;AACrB,QAAA,MAAM,oBAAoB,oBAAqB,CAAA,KAAA;AAC/C,QAAA,IAAI,CAAC,iBAAA;AACH,UAAA,OAAO,MAAM,IAAA;AAEf,QAAM,MAAA,EAAE,WAAAA,UAAW,EAAA,SAAA,EAAAC,YAAW,EAAIC,EAAAA,QAAAA,EAAS,gBAAiB,EAAA,GAAI,kBAAmB,CAAA,KAAA;AACnF,QAAA,MAAM,EAAE,MAAA,EAAQ,UAAY,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAE9D,QAAA,MAAM,EAAE,aAAA,EAAkBD,GAAAA,UAAAA,IAAa,EAAC;AAExC,QAAA,MAAM,YAAe,GAAA,qBAAA;AAAA,UACnBC,QAAAA;AAAA,UACA,YAAA;AAAA,UACA;AAAA,SACF;AAEA,QAAA,IAAI,KAAQ,GAAA,wBAAA;AAAA,UACV,KAAA;AAAA,UACA,YAAA;AAAA,UACAF,UAAAA;AAAA,UACAC,UAAAA;AAAA,UACA,gBAAA;AAAA,UACA;AAAA,SACF;AACA,QAAA,IAAI,KAAU,KAAA,CAAA;AACZ,UAAA;AAGF,QAAA,MAAM,eAAeD,UAAc,KAAA,YAAA;AACnC,QAAI,IAAA,GAAA,CAAI,UAAU,KAAS,IAAA,YAAA;AACzB,UAAA,KAAA,GAAQ,CAAC,KAAA;AAEX,QAAA,MAAM,gBAAmB,GAAA,cAAA,CAAe,GAAI,CAAA,CAAA,SAAA,KAAa,UAAU,WAAW,CAAA;AAE9E,QAAA,MAAM,aAAa,mBAAoB,CAAA;AAAA,UACrC,KAAA;AAAA,UACA,QAAQ,aAAiB,IAAA,UAAA;AAAA,UACzB,gBAAA;AAAA,UACA,YAAA;AAAA,UACA,OAAS,EAAA,SAAA,CAAU,KAAK,CAAA,GAAI,UAAa,GAAA;AAAA,SAC1C,CAAA;AAED,QAAA,MAAM,aAAgB,GAAA,CAAC,cAAe,CAAA,UAAA,EAAY,UAAU,CAAA;AAI5D,QAAA,IAAI,YAAa,CAAA,KAAK,CAAK,IAAA,YAAA,CAAa,KAAK,CAAG,EAAA;AAI9C,UAAI,IAAA,YAAA,CAAa,UAAU,KAAO,EAAA;AAChC,YAAA,YAAA,CAAa,KAAQ,GAAA,KAAA;AAErB,YAAA,IAAI,CAAC,aAAe,EAAA;AAGlB,cAAA,IAAI,YAAc,EAAA;AAChB,gBAAA,0BAAA;AAAA,kBACE,YAAA;AAAA,kBACA,KAAA,GAAQ,IAAI,uBAA0B,GAAA;AAAA,iBACxC;AAAA,eAEG,MAAA;AACH,gBAAA,0BAAA;AAAA,kBACE,YAAA;AAAA,kBACA,KAAA,GAAQ,IAAI,qBAAwB,GAAA;AAAA,iBACtC;AAAA;AACF,aAEG,MAAA;AACH,cAAA,0BAAA,CAA2B,cAAc,CAAC,CAAA;AAAA;AAC5C;AACF;AAGF,QAAA,IAAI,aAAe,EAAA;AACjB,UAAA,SAAA,CAAU,UAAU,CAAA;AAEpB,UAAA,cAAA,CAAe,MAAM,MAAS,GAAA,UAAA;AAC9B,UAAA,KAAA,CAAM,UAAU,UAAU,CAAA;AAE1B,UAAA,kBAAA;AAAA,YACE,cAAA;AAAA,YACA,UAAA;AAAA,YACA,+BAAgC,CAAA;AAAA,WAClC;AAAA;AACF,OACF;AAAA;AAGF,IAAS,SAAA,WAAA,CAAY,WAAsB,eAAyB,EAAA;AAClE,MAAA,MAAM,EAAE,MAAA,EAAQ,UAAY,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAE9D,MAAA,MAAM,wBAAwB,cAAe,CAAA,GAAA,CAAI,CAAAG,UAAAA,KAAaA,WAAU,WAAW,CAAA;AAEnF,MAAM,MAAA,EAAE,SAAW,EAAA,YAAA,EAAiB,GAAA,eAAA;AAAA,QAClC,cAAA;AAAA,QACA,SAAA;AAAA,QACA;AAAA,OACF;AAEA,MAAA,MAAA,CAAO,aAAa,IAAI,CAAA;AAExB,MAAA,MAAM,cAAc,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA,KAAM,eAAe,MAAS,GAAA,CAAA;AAC9F,MAAA,MAAM,KAAQ,GAAA,WAAA,GACV,SAAY,GAAA,eAAA,GACZ,eAAkB,GAAA,SAAA;AAEtB,MAAA,MAAM,aAAa,mBAAoB,CAAA;AAAA,QACrC,KAAA;AAAA,QACA,MAAQ,EAAA,UAAA;AAAA,QACR,gBAAkB,EAAA,qBAAA;AAAA,QAClB,YAAA;AAAA,QACA,OAAS,EAAA;AAAA,OACV,CAAA;AAED,MAAA,IAAI,CAAC,cAAA,CAAe,UAAY,EAAA,UAAU,CAAG,EAAA;AAC3C,QAAA,SAAA,CAAU,UAAU,CAAA;AAEpB,QAAA,cAAA,CAAe,MAAM,MAAS,GAAA,UAAA;AAC9B,QAAA,KAAA,CAAM,UAAU,UAAU,CAAA;AAE1B,QAAA,kBAAA;AAAA,UACE,cAAA;AAAA,UACA,UAAA;AAAA,UACA,+BAAgC,CAAA;AAAA,SAClC;AAAA;AACF;AAGF,IAAS,SAAA,0BAAA,CAA2B,WAAsB,eAAmC,EAAA;AAC3F,MAAA,MAAM,EAAE,MAAA,EAAAC,OAAQ,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAClD,MAAM,MAAA,KAAA,GAAQ,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA;AAC1D,MAAA,cAAA,CAAe,KAAK,CAAI,GAAA,SAAA;AACxB,MAAA,cAAA,CAAe,MAAM,qBAAwB,GAAA,IAAA;AAC7C,MAAM,MAAA;AAAA,QACJ,eAAe,iBAAoB,GAAA,CAAA;AAAA,QACnC,WAAa,EAAA;AAAA,OACX,GAAA,eAAA;AAEJ,MAAM,MAAA;AAAA,QACJ,eAAe,iBAAoB,GAAA,CAAA;AAAA,QACnC,WAAa,EAAA,eAAA;AAAA,QACb,SAAS,WAAc,GAAA,GAAA;AAAA,QACvB,SAAS,WAAc,GAAA;AAAA,UACrB,SAAU,CAAA,WAAA;AAEd,MAAM,MAAA,EAAE,SAAW,EAAA,aAAA,EAAkB,GAAA,eAAA;AAAA,QACnC,cAAA;AAAA,QACA,SAAA;AAAA,QACAA;AAAA,OACF;AACA,MAAA,IAAI,kBAAkB,IAAM,EAAA;AAE1B,QAAA;AAAA;AAGF,MACE,IAAA,eAAA,IACG,eACA,IAAA,aAAA,KAAkB,iBACrB,EAAA;AACA,QAAA,IAAI,sBAAsB,iBAAmB,EAAA;AAC3C,UAAA,WAAA,CAAY,WAAW,iBAAiB,CAAA;AAAA;AAI1C,OACF,MAAA,IACS,gBAAgB,WAAa,EAAA;AACpC,QAAA,WAAA,CAAY,WAAW,WAAW,CAAA;AAAA,OACpC,MAAA,IACS,gBAAgB,WAAa,EAAA;AACpC,QAAA,WAAA,CAAY,WAAW,WAAW,CAAA;AAAA;AACpC;AAGF,IAAS,SAAA,aAAA,CAAc,cAAsB,KAAoB,EAAA;AAC/D,MAAA,MAAM,EAAE,SAAA,EAAAJ,UAAU,EAAA,GAAI,kBAAmB,CAAA,KAAA;AACzC,MAAA,MAAM,EAAE,MAAA,EAAAI,OAAO,EAAA,GAAI,cAAe,CAAA,KAAA;AAClC,MAAA,IAAI,CAAC,oBAAqB,CAAA,KAAA;AACxB,QAAA;AAEF,MAAA,MAAM,aAAgB,GAAA,sBAAA;AAAA,QACpB,YAAA;AAAA,QACA,oBAAqB,CAAA;AAAA,OACvB;AACA,MAAA,MAAA,CAAO,aAAa,CAAA;AAEpB,MAAA,MAAM,qBAAwB,GAAA,4BAAA;AAAA,QAC5BJ,UAAAA;AAAA,QACA;AAAA,OACF;AAEA,MAAA,SAAA,CAAU,KAAQ,GAAA;AAAA,QAChB,YAAA;AAAA,QACA,cAAA,EAAgB,cAAc,qBAAsB,EAAA;AAAA,QACpD,qBAAA;AAAA,QACA,aAAeI,EAAAA;AAAA,OACjB;AAAA;AAEF,IAAA,SAAS,YAAe,GAAA;AACtB,MAAA,SAAA,CAAU,KAAQ,GAAA,IAAA;AAAA;AAGpB,IAAA,SAAS,gBAAgB,SAAsB,EAAA;AAC7C,MAAM,MAAA,EAAE,cAAe,EAAA,GAAI,cAAe,CAAA,KAAA;AAE1C,MAAM,MAAA,KAAA,GAAQ,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA;AAC1D,MAAA,IAAI,SAAS,CAAG,EAAA;AACd,QAAe,cAAA,CAAA,MAAA,CAAO,OAAO,CAAC,CAAA;AAM9B,QAAO,OAAA,+BAAA,CAAgC,KAAM,CAAA,SAAA,CAAU,EAAE,CAAA;AAEzD,QAAA,cAAA,CAAe,MAAM,qBAAwB,GAAA,IAAA;AAAA;AAC/C;AAGF,IAAA,SAAS,cAAc,SAAsB,EAAA;AAC3C,MAAA,MAAM,EAAE,MAAA,EAAQ,UAAY,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAE9D,MAAI,IAAA,SAAA,CAAU,YAAY,WAAa,EAAA;AACrC,QAAA,MAAM,wBAAwB,cAAe,CAAA,GAAA;AAAA,UAC3C,CAAAD,eAAaA,UAAU,CAAA;AAAA,SACzB;AAEA,QAAM,MAAA;AAAA,UACJ,aAAgB,GAAA,CAAA;AAAA,UAChB,SAAA;AAAA,UACA;AAAA,SACE,GAAA,eAAA,CAAgB,cAAgB,EAAA,SAAA,EAAW,UAAU,CAAA;AAEzD,QAAA,MAAA;AAAA,UACE,SAAa,IAAA,IAAA;AAAA,UACb,CAAA,gCAAA,EAAmC,UAAU,EAAE,CAAA,CAAA;AAAA,SACjD;AAEA,QAAA,IAAI,cAAc,aAAe,EAAA;AAG/B,UAAA,0BAAA,CAA2B,KAAM,CAAA,GAAA,CAAI,SAAU,CAAA,EAAA,EAAI,SAAS,CAAA;AAE5D,UAAA,MAAM,cACA,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA,KACxC,eAAe,MAAS,GAAA,CAAA;AAClC,UAAA,MAAM,KAAQ,GAAA,WAAA,GACV,SAAY,GAAA,aAAA,GACZ,aAAgB,GAAA,SAAA;AAEpB,UAAA,MAAM,aAAa,mBAAoB,CAAA;AAAA,YACrC,KAAA;AAAA,YACA,MAAQ,EAAA,UAAA;AAAA,YACR,gBAAkB,EAAA,qBAAA;AAAA,YAClB,YAAA;AAAA,YACA,OAAS,EAAA;AAAA,WACV,CAAA;AAED,UAAA,IAAI,CAAC,cAAA,CAAe,UAAY,EAAA,UAAU,CAAG,EAAA;AAC3C,YAAA,SAAA,CAAU,UAAU,CAAA;AAEpB,YAAA,cAAA,CAAe,MAAM,MAAS,GAAA,UAAA;AAE9B,YAAA,KAAA,CAAM,UAAU,UAAU,CAAA;AAE1B,YAAA,kBAAA;AAAA,cACE,cAAA;AAAA,cACA,UAAA;AAAA,cACA,+BAAgC,CAAA;AAAA,aAClC;AAAA;AACF;AACF;AACF;AAGF,IAAA,SAAS,YAAY,SAAsB,EAAA;AACzC,MAAA,MAAM,EAAE,MAAA,EAAQ,UAAY,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAE9D,MAAI,IAAA,SAAA,CAAU,YAAY,WAAa,EAAA;AACrC,QAAA,MAAM,wBAAwB,cAAe,CAAA,GAAA;AAAA,UAC3C,CAAAA,eAAaA,UAAU,CAAA;AAAA,SACzB;AAEA,QAAM,MAAA;AAAA,UACJ,aAAgB,GAAA,CAAA;AAAA,UAChB,SAAA;AAAA,UACA,OAAU,GAAA,CAAA;AAAA,UACV;AAAA,SACE,GAAA,eAAA,CAAgB,cAAgB,EAAA,SAAA,EAAW,UAAU,CAAA;AAEzD,QAAA,IAAI,cAAc,aAAe,EAAA;AAE/B,UAAM,MAAA,aAAA,GAAgB,2BAA2B,KAAM,CAAA,GAAA;AAAA,YACrD,SAAU,CAAA;AAAA,WACZ;AAEA,UAAA,MAAM,QACA,GAAA,aAAA,IAAiB,IAAQ,IAAA,aAAA,IAAiB,UACxC,aACA,GAAA,OAAA;AAER,UAAA,MAAM,cACA,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA,KACxC,eAAe,MAAS,GAAA,CAAA;AAClC,UAAA,MAAM,KAAQ,GAAA,WAAA,GAAc,SAAY,GAAA,QAAA,GAAW,QAAW,GAAA,SAAA;AAE9D,UAAA,MAAM,aAAa,mBAAoB,CAAA;AAAA,YACrC,KAAA;AAAA,YACA,MAAQ,EAAA,UAAA;AAAA,YACR,gBAAkB,EAAA,qBAAA;AAAA,YAClB,YAAA;AAAA,YACA,OAAS,EAAA;AAAA,WACV,CAAA;AAED,UAAA,IAAI,CAAC,cAAA,CAAe,UAAY,EAAA,UAAU,CAAG,EAAA;AAC3C,YAAA,SAAA,CAAU,UAAU,CAAA;AAEpB,YAAA,cAAA,CAAe,MAAM,MAAS,GAAA,UAAA;AAE9B,YAAA,KAAA,CAAM,UAAU,UAAU,CAAA;AAE1B,YAAA,kBAAA;AAAA,cACE,cAAA;AAAA,cACA,UAAA;AAAA,cACA,+BAAgC,CAAA;AAAA,aAClC;AAAA;AACF;AACF;AACF;AAGF,IAAA,SAAS,aAAa,SAAsB,EAAA;AAC1C,MAAA,MAAM,EAAE,MAAA,EAAAC,OAAQ,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAElD,MAAA,MAAM,EAAE,SAAU,EAAA,GAAI,eAAgB,CAAA,cAAA,EAAgB,WAAWA,OAAM,CAAA;AAEvE,MAAA,MAAA;AAAA,QACE,SAAa,IAAA,IAAA;AAAA,QACb,CAAA,gCAAA,EAAmC,UAAU,EAAE,CAAA,CAAA;AAAA,OACjD;AAEA,MAAO,OAAA,SAAA;AAAA;AAGT,IAAA,SAAS,iBAAiB,SAAsB,EAAA;AAC9C,MAAA,MAAM,EAAE,MAAA,EAAAA,OAAQ,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAElD,MAAM,MAAA;AAAA,QACJ,aAAgB,GAAA,CAAA;AAAA,QAChB,WAAA;AAAA,QACA;AAAA,OACE,GAAA,eAAA,CAAgB,cAAgB,EAAA,SAAA,EAAWA,OAAM,CAAA;AAErD,MAAA,IAAI,CAAC,WAAA;AACH,QAAO,OAAA,KAAA;AAGT,MAAA,IAAI,cAAc,MAAW,EAAA;AAC3B,QAAA,OAAO,SAAU,CAAA,WAAA,CAAY,WAAgB,KAAA,SAAA,CAAU,WAAY,CAAA,aAAA;AAAA,OAEhE,MAAA;AACH,QAAA,OAAO,SAAc,KAAA,aAAA;AAAA;AACvB;AAGF,IAAA,SAAS,gBAAgB,SAAsB,EAAA;AAC7C,MAAA,MAAM,EAAE,MAAA,EAAAA,OAAQ,EAAA,cAAA,KAAmB,cAAe,CAAA,KAAA;AAElD,MAAM,MAAA;AAAA,QACJ,aAAgB,GAAA,CAAA;AAAA,QAChB,WAAA;AAAA,QACA;AAAA,OACE,GAAA,eAAA,CAAgB,cAAgB,EAAA,SAAA,EAAWA,OAAM,CAAA;AAErD,MAAA,MAAA;AAAA,QACE,SAAa,IAAA,IAAA;AAAA,QACb,CAAA,gCAAA,EAAmC,UAAU,EAAE,CAAA,CAAA;AAAA,OACjD;AAEA,MAAO,OAAA,CAAC,eAAe,SAAY,GAAA,aAAA;AAAA;AAGrC,IAAyB,wBAAA,CAAA;AAAA,MACvB,SAAA;AAAA,MACA,WAAW,SAAU,CAAA,KAAA;AAAA,MACrB,OAAA;AAAA,MACA,0BAAA;AAAA,MACA,aAAA;AAAA,MACA,oBAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,iBAAmB,EAAA,oBAAA;AAAA,MAEnB,aAAA;AAAA,MACA,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,MACA;AAAA,KACD,CAAA;AAED,IAAS,SAAA,kBAAA,CAAmB,gBAA6B,SAAsB,EAAA;AAC7E,MAAA,OAAO,cAAe,CAAA,SAAA;AAAA,QACpB,CACE,aAAA,KAAA,aAAA,KAAkB,SAAa,IAAA,aAAA,CAAc,OAAO,SAAU,CAAA;AAAA,OAClE;AAAA;AAGF,IAAS,SAAA,eAAA,CACP,cACA,EAAA,SAAA,EACAA,OACA,EAAA;AACA,MAAM,MAAA,UAAA,GAAa,kBAAmB,CAAA,cAAA,EAAgB,SAAS,CAAA;AAE/D,MAAM,MAAA,WAAA,GAAc,UAAe,KAAA,cAAA,CAAe,MAAS,GAAA,CAAA;AAC3D,MAAM,MAAA,YAAA,GAAe,WACjB,GAAA,CAAC,UAAa,GAAA,CAAA,EAAG,UAAU,CAC3B,GAAA,CAAC,UAAY,EAAA,UAAA,GAAa,CAAC,CAAA;AAE/B,MAAM,MAAA,SAAA,GAAYA,QAAO,UAAU,CAAA;AAEnC,MAAO,OAAA;AAAA,QACL,GAAG,SAAU,CAAA,WAAA;AAAA,QACb,SAAA;AAAA,QACA;AAAA,OACF;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}