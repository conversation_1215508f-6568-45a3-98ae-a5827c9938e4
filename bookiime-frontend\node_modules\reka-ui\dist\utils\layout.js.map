{"version": 3, "file": "layout.js", "sources": ["../../src/Splitter/utils/layout.ts"], "sourcesContent": ["/* eslint-disable no-lone-blocks */\nimport type { PanelConstraints } from '../SplitterPanel.vue'\nimport { assert } from './assert'\nimport { fuzzyCompareNumbers, fuzzyNumbersEqual } from './compare'\nimport { resizePanel } from './resizePanel'\n\nexport function compareLayouts(a: number[], b: number[]) {\n  if (a.length !== b.length) {\n    return false\n  }\n  else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] !== b[index])\n        return false\n    }\n  }\n  return true\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nexport function adjustLayoutByDelta({\n  delta,\n  layout: prevLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  trigger,\n}: {\n  delta: number\n  layout: number[]\n  panelConstraints: PanelConstraints[]\n  pivotIndices: number[]\n  trigger: 'imperative-api' | 'keyboard' | 'mouse-or-touch'\n}): number[] {\n  if (fuzzyNumbersEqual(delta, 0))\n    return prevLayout\n\n  const nextLayout = [...prevLayout]\n\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices\n  assert(firstPivotIndex != null)\n  assert(secondPivotIndex != null)\n\n  let deltaApplied = 0\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta() ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === 'keyboard') {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex\n        const panelConstraints = panelConstraintsArray[index]\n        assert(panelConstraints)\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${constraints.collapsible}`);\n        if (panelConstraints.collapsible) {\n          const prevSize = prevLayout[index]\n          assert(prevSize != null)\n\n          const panelConstraints = panelConstraintsArray[index]\n          assert(panelConstraints)\n          const { collapsedSize = 0, minSize = 0 } = panelConstraints\n\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0)\n              delta = delta < 0 ? 0 - localDelta : localDelta\n              // DEBUG.push(`  -> delta: ${delta}`);\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex\n        const panelConstraints = panelConstraintsArray[index]\n        assert(panelConstraints)\n        const { collapsible } = panelConstraints\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = prevLayout[index]\n          assert(prevSize != null)\n\n          const panelConstraints = panelConstraintsArray[index]\n          assert(panelConstraints)\n          const { collapsedSize = 0, minSize = 0 } = panelConstraints\n\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0)\n              delta = delta < 0 ? 0 - localDelta : localDelta\n              // DEBUG.push(`  -> delta: ${delta}`);\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1\n\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex\n    let maxAvailableDelta = 0\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = prevLayout[index]\n      assert(prevSize != null)\n\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100,\n      })\n      const delta = maxSafeSize - prevSize\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta\n      index += increment\n\n      if (index < 0 || index >= panelConstraintsArray.length)\n        break\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta))\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex\n    let index = pivotIndex\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied)\n\n      const prevSize = prevLayout[index]\n      assert(prevSize != null)\n\n      const unsafeSize = prevSize - deltaRemaining\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize,\n      })\n\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize\n\n        nextLayout[index] = safeSize\n\n        if (\n          deltaApplied\n            .toPrecision(3)\n            .localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n              numeric: true,\n            }) >= 0\n        ) {\n          break\n        }\n      }\n\n      if (delta < 0)\n        index--\n      else\n        index++\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyNumbersEqual(deltaApplied, 0)) {\n    // console.log(DEBUG.join(\"\\n\"));\n    return prevLayout\n  }\n\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex\n\n    const prevSize = prevLayout[pivotIndex]\n    assert(prevSize != null)\n\n    const unsafeSize = prevSize + deltaApplied\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize,\n    })\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize\n\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex\n      let index = pivotIndex\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index]\n        assert(prevSize != null)\n\n        const unsafeSize = prevSize + deltaRemaining\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize,\n        })\n\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize\n\n          nextLayout[index] = safeSize\n        }\n\n        if (fuzzyNumbersEqual(deltaRemaining, 0))\n          break\n\n        if (delta > 0)\n          index--\n        else\n          index++\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0)\n  // DEBUG.push(`total size: ${totalSize}`);\n  // console.log(DEBUG.join(\"\\n\"));\n\n  if (!fuzzyNumbersEqual(totalSize, 100))\n    return prevLayout\n\n  return nextLayout\n}\n"], "names": ["panelConstraints", "delta", "pivotIndex", "prevSize", "unsafeSize", "safeSize"], "mappings": ";;;;AAMgB,SAAA,cAAA,CAAe,GAAa,CAAa,EAAA;AACvD,EAAI,IAAA,CAAA,CAAE,MAAW,KAAA,CAAA,CAAE,MAAQ,EAAA;AACzB,IAAO,OAAA,KAAA;AAAA,GAEJ,MAAA;AACH,IAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,CAAA,CAAE,QAAQ,KAAS,EAAA,EAAA;AAC7C,MAAA,IAAI,CAAE,CAAA,KAAK,CAAM,KAAA,CAAA,CAAE,KAAK,CAAA;AACtB,QAAO,OAAA,KAAA;AAAA;AACX;AAEF,EAAO,OAAA,IAAA;AACT;AAGO,SAAS,mBAAoB,CAAA;AAAA,EAClC,KAAA;AAAA,EACA,MAAQ,EAAA,UAAA;AAAA,EACR,gBAAkB,EAAA,qBAAA;AAAA,EAClB,YAAA;AAAA,EACA;AACF,CAMa,EAAA;AACX,EAAI,IAAA,iBAAA,CAAkB,OAAO,CAAC,CAAA;AAC5B,IAAO,OAAA,UAAA;AAET,EAAM,MAAA,UAAA,GAAa,CAAC,GAAG,UAAU,CAAA;AAEjC,EAAM,MAAA,CAAC,eAAiB,EAAA,gBAAgB,CAAI,GAAA,YAAA;AAC5C,EAAA,MAAA,CAAO,mBAAmB,IAAI,CAAA;AAC9B,EAAA,MAAA,CAAO,oBAAoB,IAAI,CAAA;AAE/B,EAAA,IAAI,YAAe,GAAA,CAAA;AAiBnB,EAAA;AAGE,IAAA,IAAI,YAAY,UAAY,EAAA;AAC1B,MAAA;AAEE,QAAM,MAAA,KAAA,GAAQ,KAAQ,GAAA,CAAA,GAAI,gBAAmB,GAAA,eAAA;AAC7C,QAAM,MAAA,gBAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,QAAA,MAAA,CAAO,gBAAgB,CAAA;AAIvB,QAAA,IAAI,iBAAiB,WAAa,EAAA;AAChC,UAAM,MAAA,QAAA,GAAW,WAAW,KAAK,CAAA;AACjC,UAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAEvB,UAAMA,MAAAA,iBAAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,UAAA,MAAA,CAAOA,iBAAgB,CAAA;AACvB,UAAA,MAAM,EAAE,aAAA,GAAgB,CAAG,EAAA,OAAA,GAAU,GAAMA,GAAAA,iBAAAA;AAE3C,UAAI,IAAA,iBAAA,CAAkB,QAAU,EAAA,aAAa,CAAG,EAAA;AAC9C,YAAA,MAAM,aAAa,OAAU,GAAA,QAAA;AAG7B,YAAA,IAAI,oBAAoB,UAAY,EAAA,IAAA,CAAK,GAAI,CAAA,KAAK,CAAC,CAAI,GAAA,CAAA;AACrD,cAAQ,KAAA,GAAA,KAAA,GAAQ,CAAI,GAAA,CAAA,GAAI,UAAa,GAAA,UAAA;AAAA;AAEzC;AACF;AAGF,MAAA;AAEE,QAAM,MAAA,KAAA,GAAQ,KAAQ,GAAA,CAAA,GAAI,eAAkB,GAAA,gBAAA;AAC5C,QAAM,MAAA,gBAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,QAAA,MAAA,CAAO,gBAAgB,CAAA;AACvB,QAAM,MAAA,EAAE,aAAgB,GAAA,gBAAA;AAIxB,QAAA,IAAI,WAAa,EAAA;AACf,UAAM,MAAA,QAAA,GAAW,WAAW,KAAK,CAAA;AACjC,UAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAEvB,UAAMA,MAAAA,iBAAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,UAAA,MAAA,CAAOA,iBAAgB,CAAA;AACvB,UAAA,MAAM,EAAE,aAAA,GAAgB,CAAG,EAAA,OAAA,GAAU,GAAMA,GAAAA,iBAAAA;AAE3C,UAAI,IAAA,iBAAA,CAAkB,QAAU,EAAA,OAAO,CAAG,EAAA;AACxC,YAAA,MAAM,aAAa,QAAW,GAAA,aAAA;AAG9B,YAAA,IAAI,oBAAoB,UAAY,EAAA,IAAA,CAAK,GAAI,CAAA,KAAK,CAAC,CAAI,GAAA,CAAA;AACrD,cAAQ,KAAA,GAAA,KAAA,GAAQ,CAAI,GAAA,CAAA,GAAI,UAAa,GAAA,UAAA;AAAA;AAEzC;AACF;AACF;AACF;AAIF,EAAA;AAOE,IAAM,MAAA,SAAA,GAAY,KAAQ,GAAA,CAAA,GAAI,CAAI,GAAA,EAAA;AAElC,IAAI,IAAA,KAAA,GAAQ,KAAQ,GAAA,CAAA,GAAI,gBAAmB,GAAA,eAAA;AAC3C,IAAA,IAAI,iBAAoB,GAAA,CAAA;AAGxB,IAAA,OAAO,IAAM,EAAA;AACX,MAAM,MAAA,QAAA,GAAW,WAAW,KAAK,CAAA;AACjC,MAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAEvB,MAAA,MAAM,cAAc,WAAY,CAAA;AAAA,QAC9B,gBAAkB,EAAA,qBAAA;AAAA,QAClB,UAAY,EAAA,KAAA;AAAA,QACZ,IAAM,EAAA;AAAA,OACP,CAAA;AACD,MAAA,MAAMC,SAAQ,WAAc,GAAA,QAAA;AAG5B,MAAqBA,iBAAAA,IAAAA,MAAAA;AACrB,MAAS,KAAA,IAAA,SAAA;AAET,MAAI,IAAA,KAAA,GAAQ,CAAK,IAAA,KAAA,IAAS,qBAAsB,CAAA,MAAA;AAC9C,QAAA;AAAA;AAIJ,IAAM,MAAA,WAAA,GAAc,IAAK,CAAA,GAAA,CAAI,IAAK,CAAA,GAAA,CAAI,KAAK,CAAG,EAAA,IAAA,CAAK,GAAI,CAAA,iBAAiB,CAAC,CAAA;AACzE,IAAQ,KAAA,GAAA,KAAA,GAAQ,CAAI,GAAA,CAAA,GAAI,WAAc,GAAA,WAAA;AAAA;AAKxC,EAAA;AAGE,IAAM,MAAA,UAAA,GAAa,KAAQ,GAAA,CAAA,GAAI,eAAkB,GAAA,gBAAA;AACjD,IAAA,IAAI,KAAQ,GAAA,UAAA;AACZ,IAAA,OAAO,KAAS,IAAA,CAAA,IAAK,KAAQ,GAAA,qBAAA,CAAsB,MAAQ,EAAA;AACzD,MAAA,MAAM,iBAAiB,IAAK,CAAA,GAAA,CAAI,KAAK,CAAI,GAAA,IAAA,CAAK,IAAI,YAAY,CAAA;AAE9D,MAAM,MAAA,QAAA,GAAW,WAAW,KAAK,CAAA;AACjC,MAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAEvB,MAAA,MAAM,aAAa,QAAW,GAAA,cAAA;AAC9B,MAAA,MAAM,WAAW,WAAY,CAAA;AAAA,QAC3B,gBAAkB,EAAA,qBAAA;AAAA,QAClB,UAAY,EAAA,KAAA;AAAA,QACZ,IAAM,EAAA;AAAA,OACP,CAAA;AAED,MAAA,IAAI,CAAC,iBAAA,CAAkB,QAAU,EAAA,QAAQ,CAAG,EAAA;AAC1C,QAAA,YAAA,IAAgB,QAAW,GAAA,QAAA;AAE3B,QAAA,UAAA,CAAW,KAAK,CAAI,GAAA,QAAA;AAEpB,QAAA,IACE,YACG,CAAA,WAAA,CAAY,CAAC,CAAA,CACb,aAAc,CAAA,IAAA,CAAK,GAAI,CAAA,KAAK,CAAE,CAAA,WAAA,CAAY,CAAC,CAAA,EAAG,MAAW,EAAA;AAAA,UACxD,OAAS,EAAA;AAAA,SACV,KAAK,CACR,EAAA;AACA,UAAA;AAAA;AACF;AAGF,MAAA,IAAI,KAAQ,GAAA,CAAA;AACV,QAAA,KAAA,EAAA;AAAA;AAEA,QAAA,KAAA,EAAA;AAAA;AACJ;AAQF,EAAI,IAAA,iBAAA,CAAkB,YAAc,EAAA,CAAC,CAAG,EAAA;AAEtC,IAAO,OAAA,UAAA;AAAA;AAGT,EAAA;AAEE,IAAM,MAAA,UAAA,GAAa,KAAQ,GAAA,CAAA,GAAI,gBAAmB,GAAA,eAAA;AAElD,IAAM,MAAA,QAAA,GAAW,WAAW,UAAU,CAAA;AACtC,IAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAEvB,IAAA,MAAM,aAAa,QAAW,GAAA,YAAA;AAC9B,IAAA,MAAM,WAAW,WAAY,CAAA;AAAA,MAC3B,gBAAkB,EAAA,qBAAA;AAAA,MAClB,UAAY,EAAA,UAAA;AAAA,MACZ,IAAM,EAAA;AAAA,KACP,CAAA;AAGD,IAAA,UAAA,CAAW,UAAU,CAAI,GAAA,QAAA;AAGzB,IAAA,IAAI,CAAC,iBAAA,CAAkB,QAAU,EAAA,UAAU,CAAG,EAAA;AAC5C,MAAA,IAAI,iBAAiB,UAAa,GAAA,QAAA;AAElC,MAAMC,MAAAA,WAAAA,GAAa,KAAQ,GAAA,CAAA,GAAI,gBAAmB,GAAA,eAAA;AAClD,MAAA,IAAI,KAAQA,GAAAA,WAAAA;AACZ,MAAA,OAAO,KAAS,IAAA,CAAA,IAAK,KAAQ,GAAA,qBAAA,CAAsB,MAAQ,EAAA;AACzD,QAAMC,MAAAA,SAAAA,GAAW,WAAW,KAAK,CAAA;AACjC,QAAA,MAAA,CAAOA,aAAY,IAAI,CAAA;AAEvB,QAAA,MAAMC,cAAaD,SAAW,GAAA,cAAA;AAC9B,QAAA,MAAME,YAAW,WAAY,CAAA;AAAA,UAC3B,gBAAkB,EAAA,qBAAA;AAAA,UAClB,UAAY,EAAA,KAAA;AAAA,UACZ,IAAMD,EAAAA;AAAA,SACP,CAAA;AAED,QAAA,IAAI,CAAC,iBAAA,CAAkBD,SAAUE,EAAAA,SAAQ,CAAG,EAAA;AAC1C,UAAA,cAAA,IAAkBA,SAAWF,GAAAA,SAAAA;AAE7B,UAAA,UAAA,CAAW,KAAK,CAAIE,GAAAA,SAAAA;AAAA;AAGtB,QAAI,IAAA,iBAAA,CAAkB,gBAAgB,CAAC,CAAA;AACrC,UAAA;AAEF,QAAA,IAAI,KAAQ,GAAA,CAAA;AACV,UAAA,KAAA,EAAA;AAAA;AAEA,UAAA,KAAA,EAAA;AAAA;AACJ;AACF;AAMF,EAAM,MAAA,SAAA,GAAY,WAAW,MAAO,CAAA,CAAC,OAAO,IAAS,KAAA,IAAA,GAAO,OAAO,CAAC,CAAA;AAIpE,EAAI,IAAA,CAAC,iBAAkB,CAAA,SAAA,EAAW,GAAG,CAAA;AACnC,IAAO,OAAA,UAAA;AAET,EAAO,OAAA,UAAA;AACT;;;;"}