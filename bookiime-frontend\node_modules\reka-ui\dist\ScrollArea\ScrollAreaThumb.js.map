{"version": 3, "file": "ScrollAreaThumb.js", "sources": ["../../src/ScrollArea/ScrollAreaThumb.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport interface ScrollAreaThumbProps extends PrimitiveProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { watchOnce } from '@vueuse/core'\nimport { computed, onUnmounted, ref } from 'vue'\nimport { Primitive } from '@/Primitive'\nimport { injectScrollAreaRootContext } from './ScrollAreaRoot.vue'\nimport { injectScrollAreaScrollbarVisibleContext } from './ScrollAreaScrollbarVisible.vue'\nimport { addUnlinkedScrollListener } from './utils'\n\nconst props = defineProps<ScrollAreaThumbProps>()\n\nconst rootContext = injectScrollAreaRootContext()\nconst scrollbarContextVisible = injectScrollAreaScrollbarVisibleContext()\n\nfunction handlePointerDown(event: MouseEvent) {\n  const thumb = event.target as HTMLElement\n  const thumbRect = thumb.getBoundingClientRect()\n  const x = event.clientX - thumbRect.left\n  const y = event.clientY - thumbRect.top\n  scrollbarContextVisible.handleThumbDown(event, { x, y })\n}\n\nfunction handlePointerUp(event: MouseEvent) {\n  scrollbarContextVisible.handleThumbUp(event)\n}\n\nconst { forwardRef, currentElement: thumbElement } = useForwardExpose()\nconst removeUnlinkedScrollListenerRef = ref<() => void>()\nconst viewport = computed(() => rootContext.viewport.value)\n\nfunction handleScroll() {\n  if (!removeUnlinkedScrollListenerRef.value) {\n    const listener = addUnlinkedScrollListener(\n      viewport.value!,\n      scrollbarContextVisible.onThumbPositionChange,\n    )\n    removeUnlinkedScrollListenerRef.value = listener\n    scrollbarContextVisible.onThumbPositionChange()\n  }\n}\n\nconst sizes = computed(() => scrollbarContextVisible.sizes.value)\n\nwatchOnce(sizes, () => {\n  scrollbarContextVisible.onThumbChange(thumbElement.value!)\n  if (viewport.value) {\n    /**\n     * We only bind to native scroll event so we know when scroll starts and ends.\n     * When scroll starts we start a requestAnimationFrame loop that checks for\n     * changes to scroll position. That rAF loop triggers our thumb position change\n     * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n     * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n     */\n    scrollbarContextVisible.onThumbPositionChange()\n    viewport.value.addEventListener('scroll', handleScroll)\n  }\n})\n\nonUnmounted(() => {\n  viewport.value!.removeEventListener('scroll', handleScroll)\n  rootContext.viewport.value?.removeEventListener('scroll', handleScroll)\n})\n</script>\n\n<template>\n  <Primitive\n    :ref=\"forwardRef\"\n    :data-state=\"scrollbarContextVisible.hasThumb ? 'visible' : 'hidden'\"\n    :style=\"{\n      width: 'var(--reka-scroll-area-thumb-width)',\n      height: 'var(--reka-scroll-area-thumb-height)',\n    }\"\n    :as-child=\"props.asChild\"\n    :as=\"as\"\n    @pointerdown=\"handlePointerDown\"\n    @pointerup=\"handlePointerUp\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAA,MAAM,cAAc,2BAA4B,EAAA;AAChD,IAAA,MAAM,0BAA0B,uCAAwC,EAAA;AAExE,IAAA,SAAS,kBAAkB,KAAmB,EAAA;AAC5C,MAAA,MAAM,QAAQ,KAAM,CAAA,MAAA;AACpB,MAAM,MAAA,SAAA,GAAY,MAAM,qBAAsB,EAAA;AAC9C,MAAM,MAAA,CAAA,GAAI,KAAM,CAAA,OAAA,GAAU,SAAU,CAAA,IAAA;AACpC,MAAM,MAAA,CAAA,GAAI,KAAM,CAAA,OAAA,GAAU,SAAU,CAAA,GAAA;AACpC,MAAA,uBAAA,CAAwB,eAAgB,CAAA,KAAA,EAAO,EAAE,CAAA,EAAG,GAAG,CAAA;AAAA;AAGzD,IAAA,SAAS,gBAAgB,KAAmB,EAAA;AAC1C,MAAA,uBAAA,CAAwB,cAAc,KAAK,CAAA;AAAA;AAG7C,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,YAAA,KAAiB,gBAAiB,EAAA;AACtE,IAAA,MAAM,kCAAkC,GAAgB,EAAA;AACxD,IAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,WAAA,CAAY,SAAS,KAAK,CAAA;AAE1D,IAAA,SAAS,YAAe,GAAA;AACtB,MAAI,IAAA,CAAC,gCAAgC,KAAO,EAAA;AAC1C,QAAA,MAAM,QAAW,GAAA,yBAAA;AAAA,UACf,QAAS,CAAA,KAAA;AAAA,UACT,uBAAwB,CAAA;AAAA,SAC1B;AACA,QAAA,+BAAA,CAAgC,KAAQ,GAAA,QAAA;AACxC,QAAA,uBAAA,CAAwB,qBAAsB,EAAA;AAAA;AAChD;AAGF,IAAA,MAAM,KAAQ,GAAA,QAAA,CAAS,MAAM,uBAAA,CAAwB,MAAM,KAAK,CAAA;AAEhE,IAAA,SAAA,CAAU,OAAO,MAAM;AACrB,MAAwB,uBAAA,CAAA,aAAA,CAAc,aAAa,KAAM,CAAA;AACzD,MAAA,IAAI,SAAS,KAAO,EAAA;AAQlB,QAAA,uBAAA,CAAwB,qBAAsB,EAAA;AAC9C,QAAS,QAAA,CAAA,KAAA,CAAM,gBAAiB,CAAA,QAAA,EAAU,YAAY,CAAA;AAAA;AACxD,KACD,CAAA;AAED,IAAA,WAAA,CAAY,MAAM;AAChB,MAAS,QAAA,CAAA,KAAA,CAAO,mBAAoB,CAAA,QAAA,EAAU,YAAY,CAAA;AAC1D,MAAA,WAAA,CAAY,QAAS,CAAA,KAAA,EAAO,mBAAoB,CAAA,QAAA,EAAU,YAAY,CAAA;AAAA,KACvE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}