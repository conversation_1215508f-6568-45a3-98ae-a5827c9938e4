# This is a TOML document. Boom.

title = "TOML Example"

[owner]
name = "<PERSON>"
organization = "GitHub"
bio = "GitHub Cofounder & CEO\n\tLikes \"tater tots\" and beer and backslashes: \\"
dob = 1979-05-27T07:32:00Z # First class dates? Why not?

[database]
server = "***********"
ports = [ 8001, 8001, 8003 ]
connection_max = 5000
connection_min = -2 # Don't ask me how
max_temp = 87.1 # It's a float
min_temp = -17.76
enabled = true

[servers]

  # You can indent as you please. Tabs or spaces. TOML don't care.
  [servers.alpha]
  ip = "********"
  dc = "eqdc10"

  [servers.beta]
  ip = "********"
  dc = "eqdc10"

[clients]
data = [ ["gamma", "delta"], [1, 2] ] # just an update to make sure parsers support it
