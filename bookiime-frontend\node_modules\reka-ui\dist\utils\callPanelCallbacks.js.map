{"version": 3, "file": "callPanelCallbacks.js", "sources": ["../../src/Splitter/utils/callPanelCallbacks.ts"], "sourcesContent": ["import type { PanelData } from '../SplitterPanel.vue'\nimport { assert } from './assert'\n\n// Layout should be pre-converted into percentages\nexport function callPanelCallbacks(\n  panelsArray: PanelData[],\n  layout: number[],\n  panelIdToLastNotifiedSizeMap: Record<string, number>,\n) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index]\n    assert(panelData)\n\n    const { callbacks, constraints, id: panelId } = panelData\n    const { collapsedSize = 0, collapsible } = constraints\n\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId]\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size\n\n      const { onCollapse, onExpand, onResize } = callbacks\n\n      if (onResize)\n        onResize(size, lastNotifiedSize)\n\n      if (collapsible && (onCollapse || onExpand)) {\n        if (\n          onExpand\n          && (lastNotifiedSize == null || lastNotifiedSize === collapsedSize)\n          && size !== collapsedSize\n        ) {\n          onExpand()\n        }\n\n        if (\n          onCollapse\n          && (lastNotifiedSize == null || lastNotifiedSize !== collapsedSize)\n          && size === collapsedSize\n        ) {\n          onCollapse()\n        }\n      }\n    }\n  })\n}\n"], "names": [], "mappings": ";;AAIgB,SAAA,kBAAA,CACd,WACA,EAAA,MAAA,EACA,4BACA,EAAA;AACA,EAAO,MAAA,CAAA,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAU,KAAA;AAC9B,IAAM,MAAA,SAAA,GAAY,YAAY,KAAK,CAAA;AACnC,IAAA,MAAA,CAAO,SAAS,CAAA;AAEhB,IAAA,MAAM,EAAE,SAAA,EAAW,WAAa,EAAA,EAAA,EAAI,SAAY,GAAA,SAAA;AAChD,IAAA,MAAM,EAAE,aAAA,GAAgB,CAAG,EAAA,WAAA,EAAgB,GAAA,WAAA;AAE3C,IAAM,MAAA,gBAAA,GAAmB,6BAA6B,OAAO,CAAA;AAC7D,IAAI,IAAA,gBAAA,IAAoB,IAAQ,IAAA,IAAA,KAAS,gBAAkB,EAAA;AACzD,MAAA,4BAAA,CAA6B,OAAO,CAAI,GAAA,IAAA;AAExC,MAAA,MAAM,EAAE,UAAA,EAAY,QAAU,EAAA,QAAA,EAAa,GAAA,SAAA;AAE3C,MAAI,IAAA,QAAA;AACF,QAAA,QAAA,CAAS,MAAM,gBAAgB,CAAA;AAEjC,MAAI,IAAA,WAAA,KAAgB,cAAc,QAAW,CAAA,EAAA;AAC3C,QAAA,IACE,aACI,gBAAoB,IAAA,IAAA,IAAQ,gBAAqB,KAAA,aAAA,CAAA,IAClD,SAAS,aACZ,EAAA;AACA,UAAS,QAAA,EAAA;AAAA;AAGX,QAAA,IACE,eACI,gBAAoB,IAAA,IAAA,IAAQ,gBAAqB,KAAA,aAAA,CAAA,IAClD,SAAS,aACZ,EAAA;AACA,UAAW,UAAA,EAAA;AAAA;AACb;AACF;AACF,GACD,CAAA;AACH;;;;"}