{"version": 3, "file": "SwitchThumb.cjs", "sources": ["../../src/Switch/SwitchThumb.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\n\nexport interface SwitchThumbProps extends PrimitiveProps {}\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\nimport { injectSwitchRootContext } from './SwitchRoot.vue'\n\nwithDefaults(defineProps<SwitchThumbProps>(), { as: 'span' })\n\nconst rootContext = injectSwitchRootContext()\n\nuseForwardExpose()\n</script>\n\n<template>\n  <Primitive\n    :data-state=\"rootContext.modelValue?.value ? 'checked' : 'unchecked'\"\n    :data-disabled=\"rootContext.disabled.value ? '' : undefined\"\n    :as-child=\"asChild\"\n    :as=\"as\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": ["injectSwitchRootContext", "useForwardExpose"], "mappings": ";;;;;;;;;;;;;;AAaA,IAAA,MAAM,cAAcA,yCAAwB,EAAA;AAE5C,IAAiBC,wCAAA,EAAA;;;;;;;;;;;;;;;;;;;"}