{"version": 3, "file": "StepperTitle.cjs", "sources": ["../../src/Stepper/StepperTitle.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\nimport { injectStepperItemContext } from './StepperItem.vue'\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\n\nexport interface StepperTitleProps extends PrimitiveProps { }\n\nconst props = withDefaults(defineProps<StepperTitleProps>(), { as: 'h4' })\nconst itemContext = injectStepperItemContext()\nuseForwardExpose()\n</script>\n\n<template>\n  <Primitive\n    v-bind=\"props\"\n    :id=\"itemContext.titleId\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": ["injectStepperItemContext", "useForwardExpose"], "mappings": ";;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AACd,IAAA,MAAM,cAAcA,4CAAyB,EAAA;AAC7C,IAAiBC,wCAAA,EAAA;;;;;;;;;;;;;;;;"}