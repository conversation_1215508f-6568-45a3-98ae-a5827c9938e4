{"version": 3, "file": "TabsContent.cjs", "sources": ["../../src/Tabs/TabsContent.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport type { StringOrNumber } from '@/shared/types'\nimport { useForwardExpose } from '@/shared'\n\nexport interface TabsContentProps extends PrimitiveProps {\n  /** A unique value that associates the content with a trigger. */\n  value: StringOrNumber\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with Vue animation libraries.\n   */\n  forceMount?: boolean\n}\n</script>\n\n<script setup lang=\"ts\">\nimport { computed, onMounted, ref } from 'vue'\nimport { Presence } from '@/Presence'\nimport { Primitive } from '@/Primitive'\nimport { injectTabsRootContext } from './TabsRoot.vue'\nimport { makeContentId, makeTriggerId } from './utils'\n\nconst props = defineProps<TabsContentProps>()\n\nconst { forwardRef } = useForwardExpose()\nconst rootContext = injectTabsRootContext()\nconst triggerId = computed(() => makeTriggerId(rootContext.baseId, props.value))\nconst contentId = computed(() => makeContentId(rootContext.baseId, props.value))\n\nconst isSelected = computed(() => props.value === rootContext.modelValue.value)\n\nconst isMountAnimationPreventedRef = ref(isSelected.value)\n\nonMounted(() => {\n  requestAnimationFrame(() => {\n    isMountAnimationPreventedRef.value = false\n  })\n})\n</script>\n\n<template>\n  <Presence\n    v-slot=\"{ present }\"\n    :present=\"forceMount || isSelected\"\n    force-mount\n  >\n    <Primitive\n      :id=\"contentId\"\n      :ref=\"forwardRef\"\n      :as-child=\"asChild\"\n      :as=\"as\"\n      role=\"tabpanel\"\n      :data-state=\"isSelected ? 'active' : 'inactive'\"\n      :data-orientation=\"rootContext.orientation.value\"\n      :aria-labelledby=\"triggerId\"\n      :hidden=\"!present\"\n      tabindex=\"0\"\n      :style=\"{\n        animationDuration: isMountAnimationPreventedRef ? '0s' : undefined,\n      }\"\n    >\n      <slot v-if=\"rootContext.unmountOnHide.value ? present : true\" />\n    </Primitive>\n  </Presence>\n</template>\n"], "names": ["useForwardExpose", "injectTabsRootContext", "computed", "makeTriggerId", "makeContentId", "ref", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;AAuBA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAM,MAAA,EAAE,UAAW,EAAA,GAAIA,wCAAiB,EAAA;AACxC,IAAA,MAAM,cAAcC,mCAAsB,EAAA;AAC1C,IAAM,MAAA,SAAA,GAAYC,aAAS,MAAMC,wBAAA,CAAc,YAAY,MAAQ,EAAA,KAAA,CAAM,KAAK,CAAC,CAAA;AAC/E,IAAM,MAAA,SAAA,GAAYD,aAAS,MAAME,wBAAA,CAAc,YAAY,MAAQ,EAAA,KAAA,CAAM,KAAK,CAAC,CAAA;AAE/E,IAAA,MAAM,aAAaF,YAAS,CAAA,MAAM,MAAM,KAAU,KAAA,WAAA,CAAY,WAAW,KAAK,CAAA;AAE9E,IAAM,MAAA,4BAAA,GAA+BG,OAAI,CAAA,UAAA,CAAW,KAAK,CAAA;AAEzD,IAAAC,aAAA,CAAU,MAAM;AACd,MAAA,qBAAA,CAAsB,MAAM;AAC1B,QAAA,4BAAA,CAA6B,KAAQ,GAAA,KAAA;AAAA,OACtC,CAAA;AAAA,KACF,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}