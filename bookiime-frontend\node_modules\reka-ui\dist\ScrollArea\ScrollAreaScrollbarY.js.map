{"version": 3, "file": "ScrollAreaScrollbarY.js", "sources": ["../../src/ScrollArea/ScrollAreaScrollbarY.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed, onMounted } from 'vue'\nimport { useForwardExpose } from '@/shared'\nimport { injectScrollAreaRootContext } from './ScrollAreaRoot.vue'\nimport ScrollAreaScrollbarImpl from './ScrollAreaScrollbarImpl.vue'\nimport { injectScrollAreaScrollbarVisibleContext } from './ScrollAreaScrollbarVisible.vue'\nimport { getThumbSize } from './utils'\n\nconst rootContext = injectScrollAreaRootContext()\nconst scrollbarVisibleContext = injectScrollAreaScrollbarVisibleContext()\n\nconst { forwardRef, currentElement: scrollbarElement } = useForwardExpose()\n\nonMounted(() => {\n  if (scrollbarElement.value)\n    rootContext.onScrollbarYChange(scrollbarElement.value)\n})\n\nconst sizes = computed(() => scrollbarVisibleContext.sizes.value)\n</script>\n\n<template>\n  <ScrollAreaScrollbarImpl\n    :ref=\"forwardRef\"\n    :is-horizontal=\"false\"\n    data-orientation=\"vertical\"\n    :style=\"{\n      top: 0,\n      right: rootContext.dir.value === 'ltr' ? 0 : undefined,\n      left: rootContext.dir.value === 'rtl' ? 0 : undefined,\n      bottom: 'var(--reka-scroll-area-corner-height)',\n      ['--reka-scroll-area-thumb-height' as any]: sizes ? `${getThumbSize(sizes)}px` : undefined,\n    }\"\n    @on-drag-scroll=\"scrollbarVisibleContext.onDragScroll($event.y)\"\n  >\n    <slot />\n  </ScrollAreaScrollbarImpl>\n</template>\n"], "names": [], "mappings": ";;;;;;;;;;AAQA,IAAA,MAAM,cAAc,2BAA4B,EAAA;AAChD,IAAA,MAAM,0BAA0B,uCAAwC,EAAA;AAExE,IAAA,MAAM,EAAE,UAAA,EAAY,cAAgB,EAAA,gBAAA,KAAqB,gBAAiB,EAAA;AAE1E,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,IAAI,gBAAiB,CAAA,KAAA;AACnB,QAAY,WAAA,CAAA,kBAAA,CAAmB,iBAAiB,KAAK,CAAA;AAAA,KACxD,CAAA;AAED,IAAA,MAAM,KAAQ,GAAA,QAAA,CAAS,MAAM,uBAAA,CAAwB,MAAM,KAAK,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;"}