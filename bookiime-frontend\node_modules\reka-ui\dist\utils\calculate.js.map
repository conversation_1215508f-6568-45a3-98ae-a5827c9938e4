{"version": 3, "file": "calculate.js", "sources": ["../../src/Splitter/utils/calculate.ts"], "sourcesContent": ["import type { PanelData } from '../SplitterPanel.vue'\nimport type { Direction, DragState, ResizeEvent } from './types'\nimport { assert } from './assert'\nimport { getPanelGroupElement, getResizeHandleElement } from './dom'\nimport { getResizeEventCursorPosition, isKeyDown } from './events'\n\nexport function calculateDragOffsetPercentage(\n  event: ResizeEvent,\n  dragHandleId: string,\n  direction: Direction,\n  initialDragState: DragState,\n  panelGroupElement: HTMLElement,\n): number {\n  const isHorizontal = direction === 'horizontal'\n\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement)\n  assert(handleElement)\n\n  const groupId = handleElement.getAttribute('data-panel-group-id')\n  assert(groupId)\n\n  const { initialCursorPosition } = initialDragState\n\n  const cursorPosition = getResizeEventCursorPosition(direction, event)\n\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement)\n  assert(groupElement)\n\n  const groupRect = groupElement.getBoundingClientRect()\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height\n\n  const offsetPixels = cursorPosition - initialCursorPosition\n  const offsetPercentage = (offsetPixels / groupSizeInPixels) * 100\n\n  return offsetPercentage\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nexport function calculateDeltaPercentage(\n  event: ResizeEvent,\n  dragHandleId: string,\n  direction: Direction,\n  initialDragState: DragState | null,\n  keyboardResizeBy: number | null,\n  panelGroupElement: HTMLElement,\n): number {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === 'horizontal'\n\n    let delta = 0\n    if (event.shiftKey)\n      delta = 100\n    else\n      delta = keyboardResizeBy ?? 10\n\n    let movement = 0\n    switch (event.key) {\n      case 'ArrowDown':\n        movement = isHorizontal ? 0 : delta\n        break\n      case 'ArrowLeft':\n        movement = isHorizontal ? -delta : 0\n        break\n      case 'ArrowRight':\n        movement = isHorizontal ? delta : 0\n        break\n      case 'ArrowUp':\n        movement = isHorizontal ? 0 : -delta\n        break\n      case 'End':\n        movement = 100\n        break\n      case 'Home':\n        movement = -100\n        break\n    }\n\n    return movement\n  }\n  else {\n    if (initialDragState == null)\n      return 0\n\n    return calculateDragOffsetPercentage(\n      event,\n      dragHandleId,\n      direction,\n      initialDragState,\n      panelGroupElement,\n    )\n  }\n}\n\nexport function calculateAriaValues({\n  layout,\n  panelsArray,\n  pivotIndices,\n}: {\n  layout: number[]\n  panelsArray: PanelData[]\n  pivotIndices: number[]\n}) {\n  let currentMinSize = 0\n  let currentMaxSize = 100\n  let totalMinSize = 0\n  let totalMaxSize = 0\n\n  const firstIndex = pivotIndices[0]\n  assert(firstIndex != null)\n\n  // A panel's effective min/max sizes also need to account for other panel's sizes.\n  panelsArray.forEach((panelData, index) => {\n    const { constraints } = panelData\n    const { maxSize = 100, minSize = 0 } = constraints\n\n    if (index === firstIndex) {\n      currentMinSize = minSize\n      currentMaxSize = maxSize\n    }\n    else {\n      totalMinSize += minSize\n      totalMaxSize += maxSize\n    }\n  })\n\n  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize)\n  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize)\n\n  const valueNow = layout[firstIndex]\n\n  return {\n    valueMax,\n    valueMin,\n    valueNow,\n  }\n}\n\nexport function calculateUnsafeDefaultLayout({\n  panelDataArray,\n}: {\n  panelDataArray: PanelData[]\n}): number[] {\n  const layout: number[] = Array.from({ length: panelDataArray.length })\n\n  const panelConstraintsArray = panelDataArray.map(\n    panelData => panelData.constraints,\n  )\n\n  let numPanelsWithSizes = 0\n  let remainingSize = 100\n\n  // Distribute default sizes first\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index]\n    assert(panelConstraints)\n    const { defaultSize } = panelConstraints\n\n    if (defaultSize != null) {\n      numPanelsWithSizes++\n      layout[index] = defaultSize\n      remainingSize -= defaultSize\n    }\n  }\n\n  // Remaining size should be distributed evenly between panels without default sizes\n  for (let index = 0; index < panelDataArray.length; index++) {\n    const panelConstraints = panelConstraintsArray[index]\n    assert(panelConstraints)\n    const { defaultSize } = panelConstraints\n\n    if (defaultSize != null)\n      continue\n\n    const numRemainingPanels = panelDataArray.length - numPanelsWithSizes\n    const size = remainingSize / numRemainingPanels\n\n    numPanelsWithSizes++\n    layout[index] = size\n    remainingSize -= size\n  }\n\n  return layout\n}\n"], "names": [], "mappings": ";;;;AAMO,SAAS,6BACd,CAAA,KAAA,EACA,YACA,EAAA,SAAA,EACA,kBACA,iBACQ,EAAA;AACR,EAAA,MAAM,eAAe,SAAc,KAAA,YAAA;AAEnC,EAAM,MAAA,aAAA,GAAgB,sBAAuB,CAAA,YAAA,EAAc,iBAAiB,CAAA;AAC5E,EAAA,MAAA,CAAO,aAAa,CAAA;AAEpB,EAAM,MAAA,OAAA,GAAU,aAAc,CAAA,YAAA,CAAa,qBAAqB,CAAA;AAChE,EAAA,MAAA,CAAO,OAAO,CAAA;AAEd,EAAM,MAAA,EAAE,uBAA0B,GAAA,gBAAA;AAElC,EAAM,MAAA,cAAA,GAAiB,4BAA6B,CAAA,SAAA,EAAW,KAAK,CAAA;AAEpE,EAAM,MAAA,YAAA,GAAe,oBAAqB,CAAA,OAAA,EAAS,iBAAiB,CAAA;AACpE,EAAA,MAAA,CAAO,YAAY,CAAA;AAEnB,EAAM,MAAA,SAAA,GAAY,aAAa,qBAAsB,EAAA;AACrD,EAAA,MAAM,iBAAoB,GAAA,YAAA,GAAe,SAAU,CAAA,KAAA,GAAQ,SAAU,CAAA,MAAA;AAErE,EAAA,MAAM,eAAe,cAAiB,GAAA,qBAAA;AACtC,EAAM,MAAA,gBAAA,GAAoB,eAAe,iBAAqB,GAAA,GAAA;AAE9D,EAAO,OAAA,gBAAA;AACT;AAGO,SAAS,yBACd,KACA,EAAA,YAAA,EACA,SACA,EAAA,gBAAA,EACA,kBACA,iBACQ,EAAA;AACR,EAAI,IAAA,SAAA,CAAU,KAAK,CAAG,EAAA;AACpB,IAAA,MAAM,eAAe,SAAc,KAAA,YAAA;AAEnC,IAAA,IAAI,KAAQ,GAAA,CAAA;AACZ,IAAA,IAAI,KAAM,CAAA,QAAA;AACR,MAAQ,KAAA,GAAA,GAAA;AAAA;AAER,MAAA,KAAA,GAAQ,gBAAoB,IAAA,EAAA;AAE9B,IAAA,IAAI,QAAW,GAAA,CAAA;AACf,IAAA,QAAQ,MAAM,GAAK;AAAA,MACjB,KAAK,WAAA;AACH,QAAA,QAAA,GAAW,eAAe,CAAI,GAAA,KAAA;AAC9B,QAAA;AAAA,MACF,KAAK,WAAA;AACH,QAAW,QAAA,GAAA,YAAA,GAAe,CAAC,KAAQ,GAAA,CAAA;AACnC,QAAA;AAAA,MACF,KAAK,YAAA;AACH,QAAA,QAAA,GAAW,eAAe,KAAQ,GAAA,CAAA;AAClC,QAAA;AAAA,MACF,KAAK,SAAA;AACH,QAAW,QAAA,GAAA,YAAA,GAAe,IAAI,CAAC,KAAA;AAC/B,QAAA;AAAA,MACF,KAAK,KAAA;AACH,QAAW,QAAA,GAAA,GAAA;AACX,QAAA;AAAA,MACF,KAAK,MAAA;AACH,QAAW,QAAA,GAAA,IAAA;AACX,QAAA;AAAA;AAGJ,IAAO,OAAA,QAAA;AAAA,GAEJ,MAAA;AACH,IAAA,IAAI,gBAAoB,IAAA,IAAA;AACtB,MAAO,OAAA,CAAA;AAET,IAAO,OAAA,6BAAA;AAAA,MACL,KAAA;AAAA,MACA,YAAA;AAAA,MACA,SAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA,KACF;AAAA;AAEJ;AAEO,SAAS,mBAAoB,CAAA;AAAA,EAClC,MAAA;AAAA,EACA,WAAA;AAAA,EACA;AACF,CAIG,EAAA;AACD,EAAA,IAAI,cAAiB,GAAA,CAAA;AACrB,EAAA,IAAI,cAAiB,GAAA,GAAA;AACrB,EAAA,IAAI,YAAe,GAAA,CAAA;AACnB,EAAA,IAAI,YAAe,GAAA,CAAA;AAEnB,EAAM,MAAA,UAAA,GAAa,aAAa,CAAC,CAAA;AACjC,EAAA,MAAA,CAAO,cAAc,IAAI,CAAA;AAGzB,EAAY,WAAA,CAAA,OAAA,CAAQ,CAAC,SAAA,EAAW,KAAU,KAAA;AACxC,IAAM,MAAA,EAAE,aAAgB,GAAA,SAAA;AACxB,IAAA,MAAM,EAAE,OAAA,GAAU,GAAK,EAAA,OAAA,GAAU,GAAM,GAAA,WAAA;AAEvC,IAAA,IAAI,UAAU,UAAY,EAAA;AACxB,MAAiB,cAAA,GAAA,OAAA;AACjB,MAAiB,cAAA,GAAA,OAAA;AAAA,KAEd,MAAA;AACH,MAAgB,YAAA,IAAA,OAAA;AAChB,MAAgB,YAAA,IAAA,OAAA;AAAA;AAClB,GACD,CAAA;AAED,EAAA,MAAM,QAAW,GAAA,IAAA,CAAK,GAAI,CAAA,cAAA,EAAgB,MAAM,YAAY,CAAA;AAC5D,EAAA,MAAM,QAAW,GAAA,IAAA,CAAK,GAAI,CAAA,cAAA,EAAgB,MAAM,YAAY,CAAA;AAE5D,EAAM,MAAA,QAAA,GAAW,OAAO,UAAU,CAAA;AAElC,EAAO,OAAA;AAAA,IACL,QAAA;AAAA,IACA,QAAA;AAAA,IACA;AAAA,GACF;AACF;AAEO,SAAS,4BAA6B,CAAA;AAAA,EAC3C;AACF,CAEa,EAAA;AACX,EAAA,MAAM,SAAmB,KAAM,CAAA,IAAA,CAAK,EAAE,MAAQ,EAAA,cAAA,CAAe,QAAQ,CAAA;AAErE,EAAA,MAAM,wBAAwB,cAAe,CAAA,GAAA;AAAA,IAC3C,eAAa,SAAU,CAAA;AAAA,GACzB;AAEA,EAAA,IAAI,kBAAqB,GAAA,CAAA;AACzB,EAAA,IAAI,aAAgB,GAAA,GAAA;AAGpB,EAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,cAAA,CAAe,QAAQ,KAAS,EAAA,EAAA;AAC1D,IAAM,MAAA,gBAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,IAAA,MAAA,CAAO,gBAAgB,CAAA;AACvB,IAAM,MAAA,EAAE,aAAgB,GAAA,gBAAA;AAExB,IAAA,IAAI,eAAe,IAAM,EAAA;AACvB,MAAA,kBAAA,EAAA;AACA,MAAA,MAAA,CAAO,KAAK,CAAI,GAAA,WAAA;AAChB,MAAiB,aAAA,IAAA,WAAA;AAAA;AACnB;AAIF,EAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,cAAA,CAAe,QAAQ,KAAS,EAAA,EAAA;AAC1D,IAAM,MAAA,gBAAA,GAAmB,sBAAsB,KAAK,CAAA;AACpD,IAAA,MAAA,CAAO,gBAAgB,CAAA;AACvB,IAAM,MAAA,EAAE,aAAgB,GAAA,gBAAA;AAExB,IAAA,IAAI,WAAe,IAAA,IAAA;AACjB,MAAA;AAEF,IAAM,MAAA,kBAAA,GAAqB,eAAe,MAAS,GAAA,kBAAA;AACnD,IAAA,MAAM,OAAO,aAAgB,GAAA,kBAAA;AAE7B,IAAA,kBAAA,EAAA;AACA,IAAA,MAAA,CAAO,KAAK,CAAI,GAAA,IAAA;AAChB,IAAiB,aAAA,IAAA,IAAA;AAAA;AAGnB,EAAO,OAAA,MAAA;AACT;;;;"}