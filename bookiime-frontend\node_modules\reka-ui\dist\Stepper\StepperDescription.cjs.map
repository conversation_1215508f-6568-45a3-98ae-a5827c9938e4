{"version": 3, "file": "StepperDescription.cjs", "sources": ["../../src/Stepper/StepperDescription.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport type { PrimitiveProps } from '@/Primitive'\nimport { useForwardExpose } from '@/shared'\nimport { injectStepperItemContext } from './StepperItem.vue'\n</script>\n\n<script setup lang=\"ts\">\nimport { Primitive } from '@/Primitive'\n\nexport interface StepperDescriptionProps extends PrimitiveProps { }\n\nconst props = withDefaults(defineProps<StepperDescriptionProps>(), { as: 'p' })\n\nuseForwardExpose()\nconst itemContext = injectStepperItemContext()\n</script>\n\n<template>\n  <Primitive\n    v-bind=\"props\"\n    :id=\"itemContext.descriptionId\"\n  >\n    <slot />\n  </Primitive>\n</template>\n"], "names": ["useForwardExpose", "injectStepperItemContext"], "mappings": ";;;;;;;;;;;;;;AAWA,IAAA,MAAM,KAAQ,GAAA,OAAA;AAEd,IAAiBA,wCAAA,EAAA;AACjB,IAAA,MAAM,cAAcC,4CAAyB,EAAA;;;;;;;;;;;;;;;;"}