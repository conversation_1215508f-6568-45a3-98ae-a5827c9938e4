'use strict';

const utils_events = require('./events.cjs');
const utils_rects = require('./rects.cjs');
const utils_stackingOrder = require('./stackingOrder.cjs');
const utils_style = require('./style.cjs');

const EXCEEDED_HORIZONTAL_MIN = 1;
const EXCEEDED_HORIZONTAL_MAX = 2;
const EXCEEDED_VERTICAL_MIN = 4;
const EXCEEDED_VERTICAL_MAX = 8;
function getInputType() {
  if (typeof matchMedia === "function")
    return matchMedia("(pointer:coarse)").matches ? "coarse" : "fine";
}
const isCoarsePointer = getInputType() === "coarse";
const intersectingHandles = [];
let isPointerDown = false;
const ownerDocumentCounts = /* @__PURE__ */ new Map();
const panelConstraintFlags = /* @__PURE__ */ new Map();
const registeredResizeHandlers = /* @__PURE__ */ new Set();
function registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {
  const { ownerDocument } = element;
  const data = {
    direction,
    element,
    hitAreaMargins,
    setResizeHandlerState
  };
  const count = ownerDocumentCounts.get(ownerDocument) ?? 0;
  ownerDocumentCounts.set(ownerDocument, count + 1);
  registeredResizeHandlers.add(data);
  updateListeners();
  return function unregisterResizeHandle() {
    panelConstraintFlags.delete(resizeHandleId);
    registeredResizeHandlers.delete(data);
    const count2 = ownerDocumentCounts.get(ownerDocument) ?? 1;
    ownerDocumentCounts.set(ownerDocument, count2 - 1);
    updateListeners();
    utils_style.resetGlobalCursorStyle();
    if (count2 === 1)
      ownerDocumentCounts.delete(ownerDocument);
  };
}
function handlePointerDown(event) {
  const { target } = event;
  const { x, y } = utils_events.getResizeEventCoordinates(event);
  isPointerDown = true;
  recalculateIntersectingHandles({ target, x, y });
  updateListeners();
  if (intersectingHandles.length > 0) {
    updateResizeHandlerStates("down", event);
    event.preventDefault();
  }
}
function handlePointerMove(event) {
  const { x, y } = utils_events.getResizeEventCoordinates(event);
  if (!isPointerDown) {
    const { target } = event;
    recalculateIntersectingHandles({ target, x, y });
  }
  updateResizeHandlerStates("move", event);
  updateCursor();
  if (intersectingHandles.length > 0)
    event.preventDefault();
}
function handlePointerUp(event) {
  const { target } = event;
  const { x, y } = utils_events.getResizeEventCoordinates(event);
  panelConstraintFlags.clear();
  isPointerDown = false;
  if (intersectingHandles.length > 0)
    event.preventDefault();
  updateResizeHandlerStates("up", event);
  recalculateIntersectingHandles({ target, x, y });
  updateCursor();
  updateListeners();
}
function recalculateIntersectingHandles({
  target,
  x,
  y
}) {
  intersectingHandles.splice(0);
  let targetElement = null;
  if (target instanceof HTMLElement)
    targetElement = target;
  registeredResizeHandlers.forEach((data) => {
    const { element: dragHandleElement, hitAreaMargins } = data;
    const dragHandleRect = dragHandleElement.getBoundingClientRect();
    const { bottom, left, right, top } = dragHandleRect;
    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;
    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;
    if (eventIntersects) {
      if (targetElement !== null && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) && utils_stackingOrder.compare(targetElement, dragHandleElement) > 0) {
        let currentElement = targetElement;
        let didIntersect = false;
        while (currentElement) {
          if (currentElement.contains(dragHandleElement)) {
            break;
          } else if (utils_rects.intersects(
            currentElement.getBoundingClientRect(),
            dragHandleRect)) {
            didIntersect = true;
            break;
          }
          currentElement = currentElement.parentElement;
        }
        if (didIntersect)
          return;
      }
      intersectingHandles.push(data);
    }
  });
}
function reportConstraintsViolation(resizeHandleId, flag) {
  panelConstraintFlags.set(resizeHandleId, flag);
}
function updateCursor() {
  let intersectsHorizontal = false;
  let intersectsVertical = false;
  intersectingHandles.forEach((data) => {
    const { direction } = data;
    if (direction.value === "horizontal")
      intersectsHorizontal = true;
    else
      intersectsVertical = true;
  });
  let constraintFlags = 0;
  panelConstraintFlags.forEach((flag) => {
    constraintFlags |= flag;
  });
  if (intersectsHorizontal && intersectsVertical)
    utils_style.setGlobalCursorStyle("intersection", constraintFlags);
  else if (intersectsHorizontal)
    utils_style.setGlobalCursorStyle("horizontal", constraintFlags);
  else if (intersectsVertical)
    utils_style.setGlobalCursorStyle("vertical", constraintFlags);
  else
    utils_style.resetGlobalCursorStyle();
}
function updateListeners() {
  ownerDocumentCounts.forEach((_, ownerDocument) => {
    const { body } = ownerDocument;
    body.removeEventListener("contextmenu", handlePointerUp);
    body.removeEventListener("mousedown", handlePointerDown);
    body.removeEventListener("mouseleave", handlePointerMove);
    body.removeEventListener("mousemove", handlePointerMove);
    body.removeEventListener("touchmove", handlePointerMove);
    body.removeEventListener("touchstart", handlePointerDown);
  });
  window.removeEventListener("mouseup", handlePointerUp);
  window.removeEventListener("touchcancel", handlePointerUp);
  window.removeEventListener("touchend", handlePointerUp);
  if (registeredResizeHandlers.size > 0) {
    if (isPointerDown) {
      if (intersectingHandles.length > 0) {
        ownerDocumentCounts.forEach((count, ownerDocument) => {
          const { body } = ownerDocument;
          if (count > 0) {
            body.addEventListener("contextmenu", handlePointerUp);
            body.addEventListener("mouseleave", handlePointerMove);
            body.addEventListener("mousemove", handlePointerMove);
            body.addEventListener("touchmove", handlePointerMove, {
              passive: false
            });
          }
        });
      }
      window.addEventListener("mouseup", handlePointerUp);
      window.addEventListener("touchcancel", handlePointerUp);
      window.addEventListener("touchend", handlePointerUp);
    } else {
      ownerDocumentCounts.forEach((count, ownerDocument) => {
        const { body } = ownerDocument;
        if (count > 0) {
          body.addEventListener("mousedown", handlePointerDown);
          body.addEventListener("mousemove", handlePointerMove);
          body.addEventListener("touchmove", handlePointerMove, {
            passive: false
          });
          body.addEventListener("touchstart", handlePointerDown);
        }
      });
    }
  }
}
function updateResizeHandlerStates(action, event) {
  registeredResizeHandlers.forEach((data) => {
    const { setResizeHandlerState } = data;
    const isActive = intersectingHandles.includes(data);
    setResizeHandlerState(action, isActive, event);
  });
}

exports.EXCEEDED_HORIZONTAL_MAX = EXCEEDED_HORIZONTAL_MAX;
exports.EXCEEDED_HORIZONTAL_MIN = EXCEEDED_HORIZONTAL_MIN;
exports.EXCEEDED_VERTICAL_MAX = EXCEEDED_VERTICAL_MAX;
exports.EXCEEDED_VERTICAL_MIN = EXCEEDED_VERTICAL_MIN;
exports.registerResizeHandle = registerResizeHandle;
exports.reportConstraintsViolation = reportConstraintsViolation;
//# sourceMappingURL=registry.cjs.map
